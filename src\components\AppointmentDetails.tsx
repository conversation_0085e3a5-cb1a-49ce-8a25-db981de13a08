import React from 'react';
import { Calendar, CheckCircle, XCircle, Clock, Receipt, Undo, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { AppointmentStatus } from '../types/appointment';
import { AppointmentActions } from './AppointmentActions';

interface Service {
  name: string;
  price: number;
  duration?: number;
}

interface AppointmentDetailsProps {
  appointment: {
    id: number;
    client: string;
    services: Service[];  // Update to accept array of services
    time: string;
    date: string;
    stylist: string;
    status: AppointmentStatus;
    price: number;
  };
  onStatusChange: (status: AppointmentStatus) => void;
  onReschedule: () => void;
  onCancel: () => void;
}

export function AppointmentDetails({ appointment, onStatusChange, onReschedule, onCancel }: AppointmentDetailsProps) {
  const getStatusBadge = () => {
    const badges = {
      scheduled: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      'checked-in': { color: 'bg-yellow-100 text-yellow-800', icon: CheckCircle },
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const { color, icon: Icon } = badges[appointment.status];

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
        <Icon className="w-4 h-4 mr-1" />
        {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
      </span>
    );
  };

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-100">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Appointment Details</h3>
          {getStatusBadge()}
        </div>
      </div>
      
      <div className="px-4 py-5 sm:px-6">
        <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
          <div>
            <dt className="text-sm font-medium text-gray-500">Client</dt>
            <dd className="mt-1 text-sm text-gray-900">{appointment.client}</dd>
          </div>
          <div className="sm:col-span-2">
            <dt className="text-sm font-medium text-gray-500">Services</dt>
            <dd className="mt-1 space-y-2">
              {appointment.services.map((service, index) => (
                <div key={index} className="flex justify-between text-sm text-gray-900">
                  <span>{service.name}</span>
                  <span>₹{service.price.toFixed(2)}</span>
                </div>
              ))}
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>Total</span>
                <span>₹{appointment.price.toFixed(2)}</span>
              </div>
            </dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Date & Time</dt>
            <dd className="mt-1 text-sm text-gray-900">
              {format(new Date(appointment.date), 'MMM d, yyyy')} at {appointment.time}
            </dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Stylist</dt>
            <dd className="mt-1 text-sm text-gray-900">{appointment.stylist}</dd>
          </div>
          {appointment.status === 'completed' && (
            <div className="sm:col-span-2">
              <dt className="text-sm font-medium text-gray-500">Total Amount</dt>
              <dd className="mt-1 text-sm text-gray-900">${appointment.price.toFixed(2)}</dd>
            </div>
          )}
        </dl>
      </div>

      <AppointmentActions
        status={appointment.status}
        onStatusChange={onStatusChange}
        onReschedule={onReschedule}
        onCancel={onCancel}
      />
    </div>
  );
}
