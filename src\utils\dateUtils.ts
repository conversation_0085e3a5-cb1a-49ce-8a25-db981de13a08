import { format, parseISO } from 'date-fns';

/**
 * Creates a date object with the correct local time
 * This ensures that the time displayed to the user matches what they entered
 */
export function createLocalDateTimeString(dateStr: string, timeStr: string): string {
  // Store the original time string for later use
  const originalTime = timeStr;

  // Create a new date object from the date and time strings
  const [year, month, day] = dateStr.split('-').map(Number);
  const [hours, minutes] = timeStr.split(':').map(Number);

  // Create a date object with the local time
  const date = new Date(year, month - 1, day, hours, minutes, 0, 0);

  // Store the time zone offset in minutes
  const timezoneOffset = date.getTimezoneOffset();

  // Adjust the date to account for the time zone offset
  const adjustedDate = new Date(date.getTime() - timezoneOffset * 60000);

  // Create an ISO string
  const isoString = adjustedDate.toISOString();

  // Store the original time in a global map using the ISO string as the key
  // This way we can retrieve it later when formatting the time
  originalTimeMap.set(isoString, originalTime);

  // Return the ISO string
  return isoString;
}

// Map to store original times
export const originalTimeMap = new Map<string, string>();

/**
 * Formats a date string for display
 */
export function formatDateForDisplay(dateString: string): string {
  try {
    return format(parseISO(dateString), 'MMMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
}

/**
 * Formats a time string for display
 */
export function formatTimeForDisplay(dateString: string): string {
  try {
    // Check if we have the original time in our map
    const originalTime = originalTimeMap.get(dateString);

    if (originalTime) {
      // If we have the original time, format it for display
      const [hours, minutes] = originalTime.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM
      const minutesStr = minutes < 10 ? `0${minutes}` : minutes;

      return `${hours12}:${minutesStr} ${period}`;
    }

    // Parse the ISO string
    const date = parseISO(dateString);

    // Get the hours and minutes
    const hours = date.getHours();
    const minutes = date.getMinutes();

    // Format the time in 12-hour format with AM/PM
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM
    const minutesStr = minutes < 10 ? `0${minutes}` : minutes;

    return `${hours12}:${minutesStr} ${period}`;
  } catch (error) {
    console.error('Error formatting time:', error);
    return '';
  }
}
