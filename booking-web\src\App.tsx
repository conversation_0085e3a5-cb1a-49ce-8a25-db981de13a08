import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useParams } from 'react-router-dom';
import { ClientSearch } from './components/ClientSearch';
import { ServiceSelection } from './components/ServiceSelection';
import { DateTimeSelection } from './components/DateTimeSelection';
import { BookingSummary } from './components/BookingSummary';
import { BookingConfirmation } from './components/BookingConfirmation';
import { Client } from './types/database';
import { getSalonById } from './services/salonService';
import { addAppointment } from './services/appointmentService';

type BookingStep = 'client' | 'service' | 'datetime' | 'summary' | 'confirmation';

// Component for the booking flow with a specific salon ID
function BookingFlow() {
  const { salonId } = useParams<{ salonId: string }>();
  const [currentStep, setCurrentStep] = useState<BookingStep>('client');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedDateTime, setSelectedDateTime] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [salonName, setSalonName] = useState('');

  useEffect(() => {
    const fetchSalon = async () => {
      if (!salonId) {
        setError('No salon ID provided. Please check the URL.');
        setIsLoading(false);
        return;
      }

      try {
        const salon = await getSalonById(salonId);
        if (salon) {
          setSalonName(salon.name);
        } else {
          setError('Salon not found. Please check the URL.');
        }
      } catch (error) {
        console.error('Error fetching salon:', error);
        setError('Failed to load salon information. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSalon();
  }, [salonId]);

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setCurrentStep('service');
  };

  const handleServicesSelect = (serviceIds: string[]) => {
    setSelectedServices(serviceIds);
    setCurrentStep('datetime');
  };

  const handleDateTimeSelect = (dateTime: string) => {
    setSelectedDateTime(dateTime);
    setCurrentStep('summary');
  };

  const handleBookingConfirm = async () => {
    if (!selectedClient || selectedServices.length === 0 || !selectedDateTime || !salonId) {
      setError('Missing booking information. Please try again.');
      return;
    }

    try {
      const appointment = await addAppointment({
        salon_id: salonId,
        client_id: selectedClient.client_id,
        service_ids: selectedServices,
        appointment_date: selectedDateTime,
        status: 'scheduled',
      });

      if (appointment) {
        setCurrentStep('confirmation');
      } else {
        setError('Failed to book appointment. Please try again.');
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
      setError('An error occurred while booking. Please try again.');
    }
  };

  const handleNewBooking = () => {
    // Reset all state and go back to the first step
    setSelectedClient(null);
    setSelectedServices([]);
    setSelectedDateTime('');
    setCurrentStep('client');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <p>Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-6 max-w-md">
          <h1 className="text-xl font-bold text-red-600 mb-2">Error</h1>
          <p className="mb-4">{error}</p>
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded-md"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-white shadow-sm py-4">
        <div className="container mx-auto px-4">
          <h1 className="text-xl font-bold text-center">{salonName || 'Salon'} Booking</h1>
        </div>
      </header>

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex justify-center">
            <ol className="flex items-center w-full max-w-md">
              {['Client', 'Service', 'Date & Time', 'Summary'].map((step, index) => {
                const stepValue = ['client', 'service', 'datetime', 'summary'][index] as BookingStep;
                const isActive = currentStep === stepValue;
                const isPast = 
                  (stepValue === 'client' && ['service', 'datetime', 'summary', 'confirmation'].includes(currentStep)) ||
                  (stepValue === 'service' && ['datetime', 'summary', 'confirmation'].includes(currentStep)) ||
                  (stepValue === 'datetime' && ['summary', 'confirmation'].includes(currentStep)) ||
                  (stepValue === 'summary' && ['confirmation'].includes(currentStep));
                
                return (
                  <li key={step} className={`flex items-center ${index < 3 ? 'w-full' : ''}`}>
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                      isActive ? 'bg-blue-600 text-white' : 
                      isPast ? 'bg-green-600 text-white' : 
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {isPast ? '✓' : index + 1}
                    </div>
                    <span className={`ml-2 text-sm ${
                      isActive ? 'text-blue-600 font-medium' : 
                      isPast ? 'text-green-600' : 
                      'text-gray-500'
                    }`}>
                      {step}
                    </span>
                    {index < 3 && (
                      <div className={`flex-1 h-0.5 mx-2 ${
                        isPast ? 'bg-green-600' : 'bg-gray-200'
                      }`}></div>
                    )}
                  </li>
                );
              })}
            </ol>
          </div>
        </div>

        <div className="py-4">
          {currentStep === 'client' && salonId && (
            <ClientSearch 
              onClientSelect={handleClientSelect} 
              salonId={salonId} 
            />
          )}

          {currentStep === 'service' && salonId && (
            <ServiceSelection 
              onServicesSelect={handleServicesSelect} 
              onBack={() => setCurrentStep('client')} 
              salonId={salonId} 
            />
          )}

          {currentStep === 'datetime' && salonId && (
            <DateTimeSelection 
              onDateTimeSelect={handleDateTimeSelect} 
              onBack={() => setCurrentStep('service')} 
              salonId={salonId} 
            />
          )}

          {currentStep === 'summary' && selectedClient && (
            <BookingSummary 
              client={selectedClient} 
              serviceIds={selectedServices} 
              dateTime={selectedDateTime} 
              onBack={() => setCurrentStep('datetime')} 
              onConfirm={handleBookingConfirm} 
            />
          )}

          {currentStep === 'confirmation' && (
            <BookingConfirmation 
              appointmentDate={selectedDateTime} 
              onNewBooking={handleNewBooking} 
            />
          )}
        </div>
      </main>

      <footer className="bg-white py-4 border-t">
        <div className="container mx-auto px-4 text-center text-sm text-gray-500">
          &copy; {new Date().getFullYear()} {salonName || 'Salon'} Booking. All rights reserved.
        </div>
      </footer>
    </div>
  );
}

// Main App component with routing
function App() {
  return (
    <Router>
      <Routes>
        <Route path="/salon/:salonId" element={<BookingFlow />} />
        <Route path="/" element={<Navigate to="/salon" replace />} />
        <Route path="*" element={
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center p-6 max-w-md">
              <h1 className="text-xl font-bold text-red-600 mb-2">Page Not Found</h1>
              <p className="mb-4">Please provide a salon ID in the URL.</p>
              <p className="text-sm text-gray-500">Example: /salon/your-salon-id</p>
            </div>
          </div>
        } />
      </Routes>
    </Router>
  );
}

export default App;
