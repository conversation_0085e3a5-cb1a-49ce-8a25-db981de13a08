Stack trace:
Frame         Function      Args
0007FFFF9CF0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9CF0, 0007FFFF8BF0) msys-2.0.dll+0x1FEBA
0007FFFF9CF0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FC8) msys-2.0.dll+0x67F9
0007FFFF9CF0  000210046832 (000210285FF9, 0007FFFF9BA8, 0007FFFF9CF0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9CF0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9CF0  0002100690B4 (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9FD0  00021006A49D (0007FFFF9D00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDD7100000 ntdll.dll
7FFDD6320000 KERNEL32.DLL
7FFDD4AB0000 KERNELBASE.dll
7FFDD6800000 USER32.dll
7FFDD4390000 win32u.dll
000210040000 msys-2.0.dll
7FFDD5FE0000 GDI32.dll
7FFDD47F0000 gdi32full.dll
7FFDD42E0000 msvcp_win.dll
7FFDD46A0000 ucrtbase.dll
7FFDD4F70000 advapi32.dll
7FFDD4EA0000 msvcrt.dll
7FFDD5E50000 sechost.dll
7FFDD6200000 RPCRT4.dll
7FFDD3970000 CRYPTBASE.DLL
7FFDD43C0000 bcryptPrimitives.dll
7FFDD64C0000 IMM32.DLL
