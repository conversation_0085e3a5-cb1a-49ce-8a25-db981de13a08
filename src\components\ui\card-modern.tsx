import React from 'react';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  gradient?: boolean;
  hover?: boolean;
}

export function CardModern({ 
  children, 
  className = '', 
  gradient = false,
  hover = false,
  ...props 
}: CardProps) {
  return (
    <div 
      className={`
        bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100
        ${gradient ? 'bg-gradient-to-br from-white to-gray-50' : ''}
        ${hover ? 'transition-all duration-200 hover:shadow-md hover:border-indigo-100 hover:translate-y-[-2px]' : ''}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardHeader({ 
  children, 
  className = '',
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={`px-5 py-4 border-b border-gray-100 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardTitle({ 
  children, 
  className = '',
  ...props 
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h3 
      className={`text-lg font-semibold text-gray-900 ${className}`}
      {...props}
    >
      {children}
    </h3>
  );
}

export function CardDescription({ 
  children, 
  className = '',
  ...props 
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p 
      className={`text-sm text-gray-500 mt-1 ${className}`}
      {...props}
    >
      {children}
    </p>
  );
}

export function CardContent({ 
  children, 
  className = '',
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={`px-5 py-4 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
}

export function CardFooter({ 
  children, 
  className = '',
  ...props 
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div 
      className={`px-5 py-4 bg-gray-50 border-t border-gray-100 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
}
