import React, { useEffect, useRef, useState, useCallback } from 'react';
import QRCode from 'qrcode';
import { Loader2, RefreshCw, Clock } from 'lucide-react';

interface WhatsAppQRCodeProps {
  qrData: string | null;
  qrImageUrl: string | null;
  isLoading: boolean;
  onRefresh?: () => Promise<void>;
}

export function WhatsAppQRCode({ qrData, qrImageUrl, isLoading, onRefresh }: WhatsAppQRCodeProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [timeLeft, setTimeLeft] = useState<number>(120); // 2 minutes default
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [qrGeneratedAt, setQrGeneratedAt] = useState<number>(Date.now());

  // Reset timer when new QR data is received
  useEffect(() => {
    if (qrData || qrImageUrl) {
      setQrGeneratedAt(Date.now());
      setTimeLeft(120); // Reset to 2 minutes
      setIsExpired(false);
    }
  }, [qrData, qrImageUrl]);

  // Countdown timer
  useEffect(() => {
    if (!qrData && !qrImageUrl) return;

    const interval = setInterval(() => {
      const elapsed = Math.floor((Date.now() - qrGeneratedAt) / 1000);
      const remaining = Math.max(0, 120 - elapsed);

      setTimeLeft(remaining);

      if (remaining === 0) {
        setIsExpired(true);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [qrGeneratedAt, qrData, qrImageUrl]);

  // Auto-refresh when expired (optional)
  useEffect(() => {
    if (isExpired && onRefresh) {
      // Auto-refresh after 5 seconds of expiration
      const autoRefreshTimer = setTimeout(() => {
        handleRefresh();
      }, 5000);

      return () => clearTimeout(autoRefreshTimer);
    }
  }, [isExpired, onRefresh]);

  const handleRefresh = useCallback(async () => {
    if (!onRefresh || isRefreshing) return;

    setIsRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Failed to refresh QR code:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh, isRefreshing]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    if (qrData && canvasRef.current) {
      QRCode.toCanvas(canvasRef.current, qrData, {
        width: 200,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#ffffff',
        },
      });
    }
  }, [qrData]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
        <Loader2 className="h-10 w-10 text-indigo-600 animate-spin mb-4" />
        <p className="text-gray-600 text-center">Loading QR code...</p>
      </div>
    );
  }

  if (!qrData && !qrImageUrl) {
    return (
      <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-gray-600 text-center">No QR code available</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-gray-50 rounded-lg border border-gray-200">
      {/* QR Code Display */}
      <div className={`relative ${isExpired ? 'opacity-50' : ''}`}>
        {qrImageUrl ? (
          <img
            src={qrImageUrl}
            alt="WhatsApp QR Code"
            className="w-48 h-48 mb-4"
          />
        ) : (
          <canvas ref={canvasRef} className="mb-4" />
        )}

        {/* Expired Overlay */}
        {isExpired && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg">
            <div className="text-white text-center">
              <Clock className="h-8 w-8 mx-auto mb-2" />
              <p className="text-sm font-medium">QR Code Expired</p>
            </div>
          </div>
        )}
      </div>

      {/* Timer and Status */}
      <div className="flex items-center justify-center mb-4 space-x-4">
        <div className="flex items-center space-x-2">
          <Clock className={`h-4 w-4 ${timeLeft <= 30 ? 'text-red-500' : timeLeft <= 60 ? 'text-yellow-500' : 'text-green-500'}`} />
          <span className={`text-sm font-medium ${timeLeft <= 30 ? 'text-red-600' : timeLeft <= 60 ? 'text-yellow-600' : 'text-green-600'}`}>
            {isExpired ? 'Expired' : formatTime(timeLeft)}
          </span>
        </div>

        {/* Refresh Button */}
        {onRefresh && (
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              isExpired
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        )}
      </div>

      {/* Instructions */}
      <p className="text-sm text-gray-600 text-center">
        {isExpired
          ? 'QR code has expired. Click refresh to get a new one.'
          : 'Scan this QR code with WhatsApp on your phone to connect'
        }
      </p>
      <p className="text-xs text-gray-500 text-center mt-2">
        Open WhatsApp &gt; Menu &gt; Linked Devices &gt; Link a Device
      </p>

      {/* Auto-refresh notice */}
      {isExpired && (
        <p className="text-xs text-gray-400 text-center mt-2">
          Auto-refreshing in a few seconds...
        </p>
      )}
    </div>
  );
}
