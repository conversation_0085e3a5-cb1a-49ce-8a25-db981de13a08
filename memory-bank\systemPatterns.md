# Salon POS - System Patterns

## Architecture Overview
- Single Page Application (SPA) architecture
- Client-side routing with React Router
- Component-based UI with Radix primitives
- Responsive design with Tailwind CSS
- Supabase backend integration

## Key Components
- Appointments: Manages booking and scheduling with real-time updates
- Clients: Stores client information with Supabase persistence
- Services: Maintains service catalog with pricing and duration
- Staff: Handles staff management and assignment to appointments
- Dashboard: Provides business analytics with live data
- Auth: Handles authentication via Supabase

## Data Flow
- Local state management using React hooks
- Supabase real-time subscriptions for live updates
- Optimistic UI updates for better responsiveness
- Error boundaries for graceful failure handling
- Data validation at API boundary
