import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './SimpleAuthContext';
import { supabase } from './SimpleAuthContext';

interface SalonData {
  salon_id: string;
  name: string;
  address: string;
  whatsapp_enabled: boolean;
}

interface SalonContextType {
  salonData: SalonData | null;
  loading: boolean;
  updateSalonData: (data: Partial<SalonData>) => void;
  refreshSalonData: () => Promise<void>;
}

const SalonContext = createContext<SalonContextType | undefined>(undefined);

export const useSalon = () => {
  const context = useContext(SalonContext);
  if (context === undefined) {
    throw new Error('useSalon must be used within a SalonProvider');
  }
  return context;
};

interface SalonProviderProps {
  children: ReactNode;
}

export const SalonProvider: React.FC<SalonProviderProps> = ({ children }) => {
  const [salonData, setSalonData] = useState<SalonData | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchSalonData = async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('salons')
        .select('salon_id, name, address, whatsapp_enabled')
        .eq('owner_user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching salon data:', error);
        setSalonData(null);
      } else if (data) {
        setSalonData({
          salon_id: data.salon_id,
          name: data.name || '',
          address: data.address || '',
          whatsapp_enabled: data.whatsapp_enabled || false,
        });
      }
    } catch (error) {
      console.error('Error in fetchSalonData:', error);
      setSalonData(null);
    } finally {
      setLoading(false);
    }
  };

  const updateSalonData = (data: Partial<SalonData>) => {
    setSalonData(prev => prev ? { ...prev, ...data } : null);
  };

  const refreshSalonData = async () => {
    await fetchSalonData();
  };

  useEffect(() => {
    fetchSalonData();
  }, [user]);

  const value = {
    salonData,
    loading,
    updateSalonData,
    refreshSalonData,
  };

  return <SalonContext.Provider value={value}>{children}</SalonContext.Provider>;
};
