import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    // Get count before clearing
    const { count: beforeCount } = await supabaseClient
      .from("whatsapp_message_queue")
      .select("*", { count: "exact", head: true });

    // Clear all messages from the queue
    const { error: deleteError } = await supabaseClient
      .from("whatsapp_message_queue")
      .delete()
      .neq("id", "00000000-0000-0000-0000-000000000000"); // Delete all records

    if (deleteError) {
      throw new Error(`Failed to clear queue: ${deleteError.message}`);
    }

    console.log(`🧹 Cleared ${beforeCount || 0} messages from WhatsApp queue`);

    return new Response(
      JSON.stringify({
        success: true,
        message: "WhatsApp message queue cleared successfully",
        cleared_count: beforeCount || 0,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error clearing WhatsApp queue:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      },
    );
  }
});
