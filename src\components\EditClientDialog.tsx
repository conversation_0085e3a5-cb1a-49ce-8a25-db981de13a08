import React, { useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { PhoneInput } from './ui/phone-input';

interface EditClientDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (client: { firstName: string; lastName: string; email?: string; phone: string; address?: string }) => void;
  client: {
    client_id: string;
    first_name: string;
    last_name: string;
    email: string | null;
    phone: string;
    address: string | null;
  };
}

export function EditClientDialog({ isOpen, onClose, onSubmit, client }: EditClientDialogProps) {
  const [firstName, setFirstName] = React.useState(client.first_name);
  const [lastName, setLastName] = React.useState(client.last_name);
  const [email, setEmail] = React.useState(client.email || '');
  const [phone, setPhone] = React.useState(client.phone);
  const [address, setAddress] = React.useState(client.address || '');

  // Update form when client changes
  useEffect(() => {
    setFirstName(client.first_name);
    setLastName(client.last_name);
    setEmail(client.email || '');
    setPhone(client.phone);
    setAddress(client.address || '');
  }, [client]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      firstName,
      lastName,
      email: email || undefined,
      phone,
      address: address || undefined,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">Edit Client</h2>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Optional"
              />
            </div>
          </div>
          <PhoneInput
            label="Phone"
            value={phone}
            onChange={setPhone}
            required
          />
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Optional"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Optional"
            />
          </div>
          <div className="mt-6 flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </div>
    </div>
  );
}
