import React, { useState, useEffect } from 'react';
import { Input } from './input';
import { Label } from './label';

interface PhoneInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  placeholder?: string;
  id?: string;
  name?: string;
}

export function PhoneInput({
  label,
  value,
  onChange,
  required = false,
  placeholder = 'Enter 10-digit phone number',
  id = 'phone',
  name = 'phone',
  ...props
}: PhoneInputProps) {
  const [inputValue, setInputValue] = useState(value || '');
  const [isValid, setIsValid] = useState(true);

  // Update internal state when external value changes
  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Only allow digits
    const digitsOnly = newValue.replace(/\D/g, '');

    // Limit to 10 digits
    const limitedValue = digitsOnly.slice(0, 10);

    setInputValue(limitedValue);
    onChange(limitedValue);

    // Validate: must be 10 digits if not empty or if required
    setIsValid((!required && limitedValue.length === 0) || limitedValue.length === 10);
  };

  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={id} className="flex justify-between">
          <span>{label}{required && <span className="text-red-500 ml-1">*</span>}</span>
        </Label>
      )}
      <div className="relative">
        <Input
          id={id}
          name={name}
          type="tel"
          value={inputValue}
          onChange={handleChange}
          placeholder={placeholder}
          className={!isValid ? "border-red-500 focus:ring-red-500 focus:border-red-500" : ""}
          required={required}
          maxLength={10}
          {...props}
        />
        <div className="absolute bottom-1 right-2 text-xs text-gray-400">
          {inputValue.length}/10
        </div>
      </div>
      {!isValid && (
        <p className="text-xs text-red-500 mt-1">Phone number must be 10 digits</p>
      )}
    </div>
  );
}
