import React from 'react';
import { User, Mail, Phone, Edit2, Trash2, CheckCircle } from 'lucide-react';
import { Staff } from '../../services/salonService';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback } from '../ui/avatar';

interface MobileStaffListProps {
  staff: Staff[];
  onEdit: (staff: Staff) => void;
  onActivate: (id: string) => void;
  onDeactivate: (id: string) => void;
  loading: boolean;
  searchQuery: string;
}

export function MobileStaffList({
  staff,
  onEdit,
  onActivate,
  onDeactivate,
  loading,
  searchQuery
}: MobileStaffListProps) {
  // Helper function to get initials
  const getInitials = (firstName: string, lastName?: string): string => {
    const firstInitial = firstName ? firstName.charAt(0).toUpperCase() : '';
    const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : '';
    return `${firstInitial}${lastInitial}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-10 bg-gradient-to-b from-white to-indigo-50">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (staff.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 px-4 text-center bg-gradient-to-b from-white to-indigo-50">
        <div className="h-16 w-16 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center mb-4 shadow-sm">
          <User className="h-8 w-8 text-indigo-400" />
        </div>
        <p className="text-gray-700 font-medium mb-2">
          {searchQuery ? 'No staff members match your search' : 'No staff members found'}
        </p>
        <p className="text-gray-500 text-sm mb-4">
          {!searchQuery && 'Add your first staff member to get started'}
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-100 bg-gradient-to-b from-white to-indigo-50/30">
      {staff.map((staffMember) => {
        // @ts-ignore - is_active property exists in the API response but not in the type
        const isInactive = staffMember.is_active === false;
        
        return (
          <div 
            key={staffMember.staff_id} 
            className={`p-4 ${isInactive ? 'opacity-60' : ''} hover:bg-white/80 transition-colors`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10 bg-gradient-to-br from-indigo-100 to-purple-100 text-indigo-600 shadow-sm">
                  <AvatarFallback>{getInitials(staffMember.first_name, staffMember.last_name)}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium text-gray-900">
                    {staffMember.first_name} {staffMember.last_name}
                    {staffMember.role === 'owner' && (
                      <span className="ml-2 text-xs bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full">
                        Owner
                      </span>
                    )}
                  </h3>
                  <div className="flex flex-col mt-1 space-y-1">
                    {staffMember.email && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-3.5 w-3.5 mr-1 text-indigo-400" />
                        {staffMember.email}
                      </div>
                    )}
                    {staffMember.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-3.5 w-3.5 mr-1 text-indigo-400" />
                        {staffMember.phone}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                  onClick={() => onEdit(staffMember)}
                >
                  <Edit2 className="h-4 w-4 text-indigo-500" />
                </Button>
                
                {isInactive ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                    onClick={() => onActivate(staffMember.staff_id)}
                  >
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                    onClick={() => onDeactivate(staffMember.staff_id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-400" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
