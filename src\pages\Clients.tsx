import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit2, Trash2, Loader2, Mail, Phone, MapPin, CheckCircle, Users } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { AddClientDialog } from '../components/AddClientDialog';
import { EditClientDialog } from '../components/EditClientDialog';
import { Client, getClients, addClient, deleteClient, updateClient, deactivateClient, activateClient, getCurrentUserSalonId } from '../services/salonService';
import { useToast } from '../components/ui/use-toast';
import { Avatar, AvatarFallback } from '../components/ui/avatar';
import { Pagination } from '../components/ui/pagination';
import { CardModern as Card, CardContent, CardHeader, CardTitle } from '../components/ui/card-modern';
import { MobileClientsList } from '../components/mobile/MobileClientsList';

export function Clients() {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(true);
  const [salonId, setSalonId] = useState<string | null>(null);
  const [showInactive, setShowInactive] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { toast } = useToast();

  // Fetch salon ID and clients on component mount
  useEffect(() => {
    const fetchSalonId = async () => {
      try {
        const id = await getCurrentUserSalonId();
        setSalonId(id);
        if (id) {
          fetchClients(id);
        } else {
          setLoading(false);
          toast({
            title: 'Error',
            description: 'Could not determine your salon. Please try again later.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching salon ID:', error);
        setLoading(false);
      }
    };

    fetchSalonId();
  }, []);

  // Filter clients when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredClients(clients);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredClients(
        clients.filter(
          (client) =>
            client.first_name.toLowerCase().includes(query) ||
            client.last_name.toLowerCase().includes(query) ||
            (client.email && client.email.toLowerCase().includes(query)) ||
            client.phone.includes(query)
        )
      );
    }
    // Reset to first page when search query changes
    setCurrentPage(1);
  }, [searchQuery, clients]);

  // Refetch clients when showInactive changes
  useEffect(() => {
    if (salonId) {
      fetchClients(salonId);
    }
  }, [showInactive]);

  const fetchClients = async (id: string) => {
    setLoading(true);
    try {
      const data = await getClients(id, showInactive);
      setClients(data);
      setFilteredClients(data);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        title: 'Error',
        description: 'Failed to load clients. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddClient = async (clientData: {
    firstName: string;
    lastName: string;
    email?: string;
    phone: string;
    address?: string;
  }) => {
    if (!salonId) {
      toast({
        title: 'Error',
        description: 'Could not determine your salon. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newClient = await addClient({
        salon_id: salonId,
        first_name: clientData.firstName,
        last_name: clientData.lastName,
        email: clientData.email || null,
        phone: clientData.phone,
        address: clientData.address || null,
      });

      if (newClient) {
        setClients([...clients, newClient]);
        toast({
          title: 'Success',
          description: 'Client added successfully',
        });
      }
    } catch (error) {
      console.error('Error adding client:', error);
      toast({
        title: 'Error',
        description: 'Failed to add client. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleEditClient = async (clientData: {
    firstName: string;
    lastName: string;
    email?: string;
    phone: string;
    address?: string;
  }) => {
    if (!selectedClient) return;

    try {
      const updatedClient = await updateClient(selectedClient.client_id, {
        first_name: clientData.firstName,
        last_name: clientData.lastName,
        email: clientData.email || null,
        phone: clientData.phone,
        address: clientData.address || null,
      });

      if (updatedClient) {
        // Update the clients list with the updated client
        setClients(clients.map(client =>
          client.client_id === updatedClient.client_id ? updatedClient : client
        ));
        toast({
          title: 'Success',
          description: 'Client updated successfully',
        });
      }
    } catch (error) {
      console.error('Error updating client:', error);
      toast({
        title: 'Error',
        description: 'Failed to update client. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeactivateClient = async (clientId: string) => {
    if (confirm('Are you sure you want to deactivate this client? They will no longer appear in selection lists but can be reactivated later.')) {
      try {
        const deactivatedClient = await deactivateClient(clientId);
        if (deactivatedClient) {
          // If we're not showing inactive clients, remove it from the list
          if (!showInactive) {
            setClients(clients.filter(client => client.client_id !== clientId));
          } else {
            // Otherwise, update the client in the list
            setClients(clients.map(client =>
              client.client_id === clientId ? deactivatedClient : client
            ));
          }
          toast({
            title: 'Success',
            description: 'Client deactivated successfully',
          });
        }
      } catch (error) {
        console.error('Error deactivating client:', error);
        toast({
          title: 'Error',
          description: 'Failed to deactivate client. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleActivateClient = async (clientId: string) => {
    try {
      const activatedClient = await activateClient(clientId);
      if (activatedClient) {
        setClients(clients.map(client =>
          client.client_id === clientId ? activatedClient : client
        ));
        toast({
          title: 'Success',
          description: 'Client activated successfully',
        });
      }
    } catch (error) {
      console.error('Error activating client:', error);
      toast({
        title: 'Error',
        description: 'Failed to activate client. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Keep for backward compatibility
  const handleDeleteClient = handleDeactivateClient;

  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Listen for the custom event to open the add client dialog
    const handleOpenClientDialog = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('open-client-dialog', handleOpenClientDialog);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('open-client-dialog', handleOpenClientDialog);
    };
  }, []);

  return (
    <div className="space-y-6">
      {isMobile ? (
        // Mobile view
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-indigo-500" />
              <Input
                placeholder="Search clients..."
                className="pl-10 rounded-full border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 shadow-sm bg-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center px-2 py-2 bg-white/80 rounded-lg shadow-sm border border-indigo-50">
            <input
              type="checkbox"
              id="showInactive"
              checked={showInactive}
              onChange={(e) => setShowInactive(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="showInactive" className="text-sm text-gray-700 ml-2">
              Show inactive clients
            </label>
          </div>
        </div>
      ) : (
        // Desktop view
        <Card gradient hover>
          <CardHeader className="flex flex-col sm:flex-row justify-between items-center">
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-indigo-600" />
              Clients
            </CardTitle>
            <Button
              onClick={() => setShowAddDialog(true)}
              className="mt-4 sm:mt-0 bg-indigo-600 hover:bg-indigo-700 rounded-full shadow-sm"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add Client
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Search clients..."
                  className="pl-10 rounded-lg border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                <input
                  type="checkbox"
                  id="showInactive"
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="showInactive" className="text-sm text-gray-700">
                  Show inactive clients
                </label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isMobile ? (
        // Mobile view of clients list
        <div className="rounded-xl overflow-hidden shadow-sm border border-indigo-50">
          <MobileClientsList
            clients={filteredClients.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)}
            onEdit={(client) => {
              setSelectedClient(client);
              setShowEditDialog(true);
            }}
            onActivate={handleActivateClient}
            onDeactivate={handleDeactivateClient}
            loading={loading}
            searchQuery={searchQuery}
          />

          {/* Pagination for mobile */}
          {!loading && filteredClients.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredClients.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                totalItems={filteredClients.length}
                itemsPerPage={itemsPerPage}
              />
            </div>
          )}
        </div>
      ) : (
        // Desktop view
        loading ? (
          <Card gradient className="py-10">
            <div className="flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
          </Card>
        ) : filteredClients.length === 0 ? (
          <Card gradient hover className="p-10 text-center">
            <div className="flex flex-col items-center">
              <Users className="h-12 w-12 text-gray-300 mb-4" />
              <p className="text-gray-500 mb-4">
                {searchQuery ? 'No clients match your search' : 'No clients found. Add your first client!'}
              </p>
              {!searchQuery && (
                <Button
                  onClick={() => setShowAddDialog(true)}
                  variant="outline"
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Client
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <Card gradient hover>
            <div className="divide-y divide-gray-100">
              {/* Get current clients for pagination */}
              {filteredClients
                .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                .map((client) => {
                  // @ts-ignore - is_active property exists in the API response but not in the type
                  const isInactive = client.is_active === false;

                  return (
                    <div
                      key={client.client_id}
                      className={`p-4 hover:bg-gray-50/50 transition-colors ${isInactive ? 'opacity-75 bg-gray-50/50' : ''}`}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <Avatar className="h-12 w-12 bg-indigo-100 text-indigo-600 shadow-sm">
                            <AvatarFallback>{getInitials(client.first_name, client.last_name)}</AvatarFallback>
                          </Avatar>
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center flex-wrap gap-2">
                              <h3 className="text-sm font-medium text-gray-900">
                                {client.first_name} {client.last_name}
                              </h3>
                              {isInactive && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                  Inactive
                                </span>
                              )}
                            </div>
                            <div className="flex space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 rounded-full"
                                onClick={() => {
                                  setSelectedClient(client);
                                  setShowEditDialog(true);
                                }}
                              >
                                <Edit2 className="h-4 w-4 text-gray-500" />
                              </Button>
                              {isInactive ? (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 rounded-full"
                                  onClick={() => handleActivateClient(client.client_id)}
                                  title="Activate client"
                                >
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                </Button>
                              ) : (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 rounded-full"
                                  onClick={() => handleDeactivateClient(client.client_id)}
                                  title="Deactivate client"
                                >
                                  <Trash2 className="h-4 w-4 text-red-400" />
                                </Button>
                              )}
                            </div>
                          </div>
                          <div className="mt-2 flex flex-wrap gap-x-4 gap-y-2">
                            <p className="flex items-center text-xs text-gray-500">
                              <Phone className="h-3.5 w-3.5 mr-1 text-gray-400" />
                              {client.phone}
                            </p>
                            {client.email && (
                              <p className="flex items-center text-xs text-gray-500">
                                <Mail className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                {client.email}
                              </p>
                            )}
                            {client.address && (
                              <p className="flex items-center text-xs text-gray-500">
                                <MapPin className="h-3.5 w-3.5 mr-1 text-gray-400" />
                                {client.address}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>

            {/* Pagination */}
            {filteredClients.length > 0 && (
              <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(filteredClients.length / itemsPerPage)}
                  onPageChange={setCurrentPage}
                  totalItems={filteredClients.length}
                  itemsPerPage={itemsPerPage}
                />
              </div>
            )}
          </Card>
        )
      )}

      <AddClientDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={handleAddClient}
      />

      {selectedClient && (
        <EditClientDialog
          isOpen={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          onSubmit={handleEditClient}
          client={selectedClient}
        />
      )}
    </div>
  );
}
