import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Calendar, Clock, User, Scissors, CalendarCheck, Search, CheckCircle, ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Input } from './ui/input';
import { useToast } from './ui/use-toast';
import { Client, Service, StaffMember } from '../services/salonService';
import { Badge } from './ui/badge';


interface BookAppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (appointment: {
    clientId: string;
    serviceId: string;
    staffId: string;
    appointmentDate: string; // ISO format timestamp
  }) => void;
  clients: Client[];
  services: Service[];
  staff: StaffMember[];
}

export function BookAppointmentDialog({
  isOpen,
  onClose,
  onSubmit,
  clients,
  services,
  staff
}: BookAppointmentDialogProps) {
  const [selectedClient, setSelectedClient] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [selectedStaff, setSelectedStaff] = useState('');
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [time, setTime] = useState('');
  const { toast } = useToast();

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedClient('');
      setSelectedService('');
      setSelectedStaff('');
      setDate(format(new Date(), 'yyyy-MM-dd'));
      setTime('');
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedClient || !selectedService || !selectedStaff || !date || !time) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    // Combine date and time into a single ISO timestamp
    const appointmentDate = new Date(`${date}T${time}`).toISOString();

    onSubmit({
      clientId: selectedClient,
      serviceId: selectedService,
      staffId: selectedStaff,
      appointmentDate,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6 max-h-[90vh] overflow-y-auto">
        <div className="mb-4">
          <h2 className="text-xl font-bold flex items-center">
            <CalendarCheck className="mr-2 h-5 w-5 text-blue-600" />
            Book New Appointment
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="space-y-5 py-4">
          <div className="space-y-2">
            <Label htmlFor="client" className="flex items-center text-base font-medium">
              <User className="mr-2 h-4 w-4 text-muted-foreground" />
              Client
            </Label>
            <Select value={selectedClient} onValueChange={setSelectedClient} required>
              <SelectTrigger className="bg-white shadow-sm">
                <SelectValue placeholder="Select client" />
              </SelectTrigger>
              <SelectContent>
                {clients.map((client) => (
                  <SelectItem key={client.client_id} value={client.client_id}>
                    {client.first_name} {client.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="service" className="flex items-center text-base font-medium">
              <Scissors className="mr-2 h-4 w-4 text-muted-foreground" />
              Service
            </Label>
            <Select value={selectedService} onValueChange={setSelectedService} required>
              <SelectTrigger className="bg-white shadow-sm">
                <SelectValue placeholder="Select service" />
              </SelectTrigger>
              <SelectContent>
                {services.map((service) => (
                  <SelectItem key={service.service_id} value={service.service_id}>
                    {service.name} - ${service.price} ({service.duration} min)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="staff" className="flex items-center text-base font-medium">
              <User className="mr-2 h-4 w-4 text-muted-foreground" />
              Staff Member
            </Label>
            <Select value={selectedStaff} onValueChange={setSelectedStaff} required>
              <SelectTrigger className="bg-white shadow-sm">
                <SelectValue placeholder="Select staff member" />
              </SelectTrigger>
              <SelectContent>
                {staff.map((member) => (
                  <SelectItem key={member.user_id} value={member.user_id}>
                    {member.first_name} {member.last_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="date" className="flex items-center text-base font-medium">
              <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
              Date
            </Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              min={format(new Date(), 'yyyy-MM-dd')}
              required
              className="bg-white shadow-sm"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="time" className="flex items-center text-base font-medium">
              <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
              Time
            </Label>
            <Input
              id="time"
              type="time"
              value={time}
              onChange={(e) => setTime(e.target.value)}
              className="bg-white shadow-sm"
              required
            />
          </div>

          <div className="mt-6 flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="shadow-sm hover:shadow transition-all"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-md hover:shadow-lg transition-all h-11"
              disabled={!selectedClient || !selectedService || !selectedStaff || !date || !time}
            >
              Book Appointment
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
