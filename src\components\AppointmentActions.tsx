import React from 'react';
import { Calendar, CheckCircle, XCircle, Receipt, Undo, AlertCircle } from 'lucide-react';
import { AppointmentStatus } from '../types/appointment';

interface AppointmentActionsProps {
  status: AppointmentStatus;
  onStatusChange: (status: AppointmentStatus) => void;
  onReschedule: () => void;
  onCancel: () => void;
}

export function AppointmentActions({ status, onStatusChange, onReschedule, onCancel }: AppointmentActionsProps) {
  const renderActionButtons = () => {
    switch (status) {
      case 'scheduled':
        return (
          <>
            <button
              onClick={() => onStatusChange('checked-in')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Check In
            </button>
            <button
              onClick={onReschedule}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Reschedule
            </button>
            <button
              onClick={onCancel}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Cancel
            </button>
          </>
        );

      case 'checked-in':
        return (
          <>
            <button
              onClick={() => onStatusChange('completed')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Complete
            </button>
            <button
              onClick={onReschedule}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Reschedule
            </button>
            <button
              onClick={onCancel}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Cancel
            </button>
          </>
        );

      case 'completed':
        return (
          <>
            <button
              onClick={() => {}} // View receipt handler
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Receipt className="h-4 w-4 mr-2" />
              View Receipt
            </button>
            <button
              onClick={onReschedule}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <Undo className="h-4 w-4 mr-2" />
              Book Again
            </button>
          </>
        );

      case 'cancelled':
        return (
          <button
            onClick={onReschedule}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Undo className="h-4 w-4 mr-2" />
            Book Again
          </button>
        );

      default:
        return null;
    }
  };

  return (
    <div className="px-4 py-5 sm:px-6 border-t border-gray-100 bg-gray-50 rounded-b-lg">
      <div className="flex flex-wrap gap-3 justify-end">
        {renderActionButtons()}
      </div>
    </div>
  );
}