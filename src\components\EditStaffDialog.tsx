import React, { useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { PhoneInput } from './ui/phone-input';

interface EditStaffDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (staff: { firstName: string; lastName: string; role: string; email: string; phone?: string }) => void;
  staff: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string | null;
    role: string;
  };
}

export function EditStaffDialog({ isOpen, onClose, onSubmit, staff }: EditStaffDialogProps) {
  const [firstName, setFirstName] = React.useState(staff.first_name);
  const [lastName, setLastName] = React.useState(staff.last_name);
  const [email, setEmail] = React.useState(staff.email);
  const [phone, setPhone] = React.useState(staff.phone || '');
  const [role, setRole] = React.useState(staff.role);

  // Update form when staff changes
  useEffect(() => {
    setFirstName(staff.first_name);
    setLastName(staff.last_name);
    setEmail(staff.email);
    setPhone(staff.phone || '');
    setRole(staff.role);
  }, [staff]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate phone number - must be exactly 10 digits
    if (phone.length !== 10) {
      alert("Phone number must be exactly 10 digits");
      return;
    }

    onSubmit({
      firstName,
      lastName,
      role,
      email,
      phone: phone || undefined,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">Edit Staff Member</h2>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                required
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <PhoneInput
            label="Phone"
            value={phone}
            onChange={setPhone}
            required
            placeholder="Enter 10-digit phone number"
          />
          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select value={role} onValueChange={setRole} required>
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="stylist">Stylist</SelectItem>
                <SelectItem value="colorist">Colorist</SelectItem>
                <SelectItem value="nail-technician">Nail Technician</SelectItem>
                <SelectItem value="assistant">Assistant</SelectItem>
                <SelectItem value="receptionist">Receptionist</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="owner">Owner</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="mt-6 flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </div>
    </div>
  );
}
