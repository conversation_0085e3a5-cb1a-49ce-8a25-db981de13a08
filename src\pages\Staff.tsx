import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit2, Trash2, Loader2, Mail, Phone, CheckCircle, User } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { AddStaffDialog } from '../components/AddStaffDialog';
import { EditStaffDialog } from '../components/EditStaffDialog';
import { StaffMember, getStaff, addStaffMember, deleteStaffMember, updateStaffMember, deactivateStaffMember, activateStaffMember, getCurrentUserSalonId } from '../services/salonService';
import { useToast } from '../components/ui/use-toast';
import { Avatar, AvatarFallback } from '../components/ui/avatar';
import { Pagination } from '../components/ui/pagination';
import { MobileStaffList } from '../components/mobile/MobileStaffList';
import { CardModern as Card, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from '../components/ui/card-modern';

export function Staff() {
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<StaffMember[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [loading, setLoading] = useState(true);
  const [salonId, setSalonId] = useState<string | null>(null);
  const [showInactive, setShowInactive] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { toast } = useToast();

  // Fetch salon ID and staff on component mount
  useEffect(() => {
    const fetchSalonId = async () => {
      try {
        const id = await getCurrentUserSalonId();
        setSalonId(id);
        if (id) {
          fetchStaff(id);
        } else {
          setLoading(false);
          toast({
            title: 'Error',
            description: 'Could not determine your salon. Please try again later.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching salon ID:', error);
        setLoading(false);
      }
    };

    fetchSalonId();
  }, []);

  // Filter staff when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredStaff(staff);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredStaff(
        staff.filter(
          (member) =>
            member.first_name.toLowerCase().includes(query) ||
            member.last_name.toLowerCase().includes(query) ||
            member.email.toLowerCase().includes(query) ||
            (member.phone && member.phone.includes(query))
        )
      );
    }
    // Reset to first page when search query changes
    setCurrentPage(1);
  }, [searchQuery, staff]);

  // Refetch staff when showInactive changes
  useEffect(() => {
    if (salonId) {
      fetchStaff(salonId);
    }
  }, [showInactive]);

  const fetchStaff = async (id: string) => {
    setLoading(true);
    try {
      const data = await getStaff(id, showInactive);
      setStaff(data);
      setFilteredStaff(data);
    } catch (error) {
      console.error('Error fetching staff:', error);
      toast({
        title: 'Error',
        description: 'Failed to load staff members. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddStaff = async (staffData: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    role: string;
  }) => {
    if (!salonId) {
      toast({
        title: 'Error',
        description: 'Could not determine your salon. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newStaffMember = await addStaffMember({
        salon_id: salonId,
        first_name: staffData.firstName,
        last_name: staffData.lastName,
        email: staffData.email,
        phone: staffData.phone || null,
        role: staffData.role,
      });

      if (newStaffMember) {
        setStaff([...staff, newStaffMember]);
        toast({
          title: 'Success',
          description: 'Staff member added successfully',
        });
      }
    } catch (error) {
      console.error('Error adding staff member:', error);
      toast({
        title: 'Error',
        description: 'Failed to add staff member. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleEditStaff = async (staffData: {
    firstName: string;
    lastName: string;
    role: string;
    email: string;
    phone?: string;
  }) => {
    if (!selectedStaff) return;

    try {
      const updatedStaff = await updateStaffMember(selectedStaff.user_id, {
        first_name: staffData.firstName,
        last_name: staffData.lastName,
        email: staffData.email,
        phone: staffData.phone || null,
        role: staffData.role,
      });

      if (updatedStaff) {
        // Update the staff list with the updated staff member
        setStaff(staff.map(member =>
          member.user_id === updatedStaff.user_id ? updatedStaff : member
        ));
        toast({
          title: 'Success',
          description: 'Staff member updated successfully',
        });
      }
    } catch (error) {
      console.error('Error updating staff member:', error);
      toast({
        title: 'Error',
        description: 'Failed to update staff member. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeactivateStaff = async (userId: string) => {
    if (confirm('Are you sure you want to deactivate this staff member? They will no longer appear in selection lists but can be reactivated later.')) {
      try {
        const deactivatedStaff = await deactivateStaffMember(userId);
        if (deactivatedStaff) {
          // If we're not showing inactive staff, remove it from the list
          if (!showInactive) {
            setStaff(staff.filter(member => member.user_id !== userId));
          } else {
            // Otherwise, update the staff member in the list
            setStaff(staff.map(member =>
              member.user_id === userId ? deactivatedStaff : member
            ));
          }
          toast({
            title: 'Success',
            description: 'Staff member deactivated successfully',
          });
        }
      } catch (error) {
        console.error('Error deactivating staff member:', error);
        toast({
          title: 'Error',
          description: 'Failed to deactivate staff member. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleActivateStaff = async (userId: string) => {
    try {
      const activatedStaff = await activateStaffMember(userId);
      if (activatedStaff) {
        setStaff(staff.map(member =>
          member.user_id === userId ? activatedStaff : member
        ));
        toast({
          title: 'Success',
          description: 'Staff member activated successfully',
        });
      }
    } catch (error) {
      console.error('Error activating staff member:', error);
      toast({
        title: 'Error',
        description: 'Failed to activate staff member. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Keep for backward compatibility
  const handleDeleteStaff = handleDeactivateStaff;

  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Listen for the custom event to open the add staff dialog
    const handleOpenStaffDialog = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('open-staff-dialog', handleOpenStaffDialog);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('open-staff-dialog', handleOpenStaffDialog);
    };
  }, []);

  return (
    <div className="space-y-6">
      {isMobile ? (
        // Mobile view
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-indigo-500" />
              <Input
                placeholder="Search staff..."
                className="pl-10 rounded-full border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 shadow-sm bg-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center px-2 py-2 bg-white/80 rounded-lg shadow-sm border border-indigo-50">
            <input
              type="checkbox"
              id="showInactive"
              checked={showInactive}
              onChange={(e) => setShowInactive(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="showInactive" className="text-sm text-gray-700 ml-2">
              Show inactive staff
            </label>
          </div>
        </div>
      ) : (
        // Desktop view
        <Card gradient hover>
          <CardHeader className="flex flex-col sm:flex-row justify-between items-center">
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2 text-indigo-600" />
              Staff
            </CardTitle>
            <Button
              onClick={() => setShowAddDialog(true)}
              className="mt-4 sm:mt-0 bg-indigo-600 hover:bg-indigo-700 rounded-full shadow-sm"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add Staff Member
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Search staff..."
                  className="pl-10 rounded-lg border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                <input
                  type="checkbox"
                  id="showInactive"
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="showInactive" className="text-sm text-gray-700">
                  Show inactive staff
                </label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isMobile ? (
        // Mobile view of staff list
        <div className="rounded-xl overflow-hidden shadow-sm border border-indigo-50">
          <MobileStaffList
            staff={filteredStaff.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)}
            onEdit={(staffMember) => {
              setSelectedStaff(staffMember);
              setShowEditDialog(true);
            }}
            onActivate={handleActivateStaff}
            onDeactivate={handleDeactivateStaff}
            loading={loading}
            searchQuery={searchQuery}
          />

          {/* Pagination for mobile */}
          {!loading && filteredStaff.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredStaff.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                totalItems={filteredStaff.length}
                itemsPerPage={itemsPerPage}
              />
            </div>
          )}
        </div>
      ) : (
        // Desktop view
        loading ? (
          <Card gradient className="py-10">
            <div className="flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
          </Card>
        ) : filteredStaff.length === 0 ? (
          <Card gradient hover className="p-10 text-center">
            <div className="flex flex-col items-center">
              <User className="h-12 w-12 text-gray-300 mb-4" />
              <p className="text-gray-500 mb-4">
                {searchQuery ? 'No staff members match your search' : 'No staff members found. Add your first staff member!'}
              </p>
              {!searchQuery && (
                <Button
                  onClick={() => setShowAddDialog(true)}
                  variant="outline"
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Staff Member
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <Card gradient hover>
            <ul className="divide-y divide-gray-100">
              {/* Get current staff for pagination */}
              {filteredStaff
                .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                .map((member) => (
                <li key={member.user_id} className={`p-4 hover:bg-gray-50/50 transition-colors ${member.is_active === false ? 'opacity-75 bg-gray-50/50' : ''}`}>
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Avatar className="h-10 w-10 bg-indigo-100 text-indigo-600">
                        <AvatarFallback>{getInitials(member.first_name, member.last_name)}</AvatarFallback>
                      </Avatar>
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            <h3 className="text-sm font-medium text-gray-900">
                              {member.first_name} {member.last_name}
                            </h3>
                            {member.is_active === false && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                Inactive
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 mt-1 capitalize">{member.role}</p>
                        </div>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full"
                            onClick={() => {
                              setSelectedStaff(member);
                              setShowEditDialog(true);
                            }}
                          >
                            <Edit2 className="h-4 w-4 text-gray-500" />
                          </Button>
                          {member.is_active === false ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 rounded-full"
                              onClick={() => handleActivateStaff(member.user_id)}
                              title="Activate staff member"
                            >
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 rounded-full"
                              onClick={() => handleDeactivateStaff(member.user_id)}
                              title="Deactivate staff member"
                            >
                              <Trash2 className="h-4 w-4 text-red-400" />
                            </Button>
                          )}
                        </div>
                      </div>
                      <div className="mt-2 flex flex-col sm:flex-row sm:space-x-4">
                        {member.email && (
                          <p className="flex items-center text-xs text-gray-500">
                            <Mail className="h-3.5 w-3.5 mr-1 text-gray-400" />
                            {member.email}
                          </p>
                        )}
                        {member.phone && (
                          <p className="flex items-center text-xs text-gray-500 mt-1 sm:mt-0">
                            <Phone className="h-3.5 w-3.5 mr-1 text-gray-400" />
                            {member.phone}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>

            {/* Pagination */}
            {filteredStaff.length > 0 && (
              <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(filteredStaff.length / itemsPerPage)}
                  onPageChange={setCurrentPage}
                  totalItems={filteredStaff.length}
                  itemsPerPage={itemsPerPage}
                />
              </div>
            )}
          </Card>
        )
      )}

      <AddStaffDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={handleAddStaff}
      />

      {selectedStaff && (
        <EditStaffDialog
          isOpen={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          onSubmit={handleEditStaff}
          staff={selectedStaff}
        />
      )}
    </div>
  );
}
