# Local WhatsApp Testing Setup Guide

This guide will help you set up local WhatsApp testing for the Salon Orbit Lite application.

## Quick Start

### 1. Set up WhatsApp Bot Environment

1. **Copy environment file:**
   ```bash
   cd wbot
   copy .env.example .env
   ```

2. **Edit the `.env` file** and add your Supabase service key:
   ```
   SUPABASE_URL=https://mixjfinrxzpplzqidlas.supabase.co
   SUPABASE_SERVICE_KEY=your_actual_service_key_here
   PORT=3000
   NODE_ENV=development
   ```

### 2. Start Local WhatsApp Bot

**Option A: Using the batch script (Windows)**
```bash
start-local-whatsapp.bat
```

**Option B: Manual start**
```bash
cd wbot
npm install
npm start
```

The bot will start on `http://localhost:3000`

### 3. Configure Main App for Local Testing

The main app now defaults to production WhatsApp API even in development mode for better reliability.

**To use local WhatsApp bot**, uncomment ONE of these lines in `.env.local`:

**Option 1 (Recommended):**
```
VITE_USE_LOCAL_WHATSAPP=true
```

**Option 2 (Direct URL):**
```
VITE_WHATSAPP_API_URL=http://localhost:3000
```

### 4. Start Main Application

```bash
npm run dev
```

The app will run on `http://localhost:5173`

## Testing WhatsApp Integration

### 1. Enable WhatsApp in Settings

1. Go to **Settings** in the app
2. Expand the **WhatsApp Integration** section
3. Toggle **Enable WhatsApp Notifications** to ON
4. Click **Connect WhatsApp**

### 2. Scan QR Code

1. A QR code will appear in the app
2. Open WhatsApp on your phone
3. Go to **Menu** > **Linked Devices** > **Link a Device**
4. Scan the QR code
5. Wait for connection confirmation

### 3. Test Message Sending

1. Create a test appointment with a valid phone number
2. Check the browser console for WhatsApp API calls
3. Check the WhatsApp bot terminal for message processing logs
4. Verify the client receives the WhatsApp message

## Troubleshooting

### Bot Not Starting

- **Check Node.js version:** Ensure you have Node.js 16+ installed
- **Check dependencies:** Run `npm install` in the `wbot` directory
- **Check environment:** Verify your `.env` file has the correct Supabase credentials

### Connection Issues

- **CORS errors:** The local bot includes CORS headers for development
- **Port conflicts:** Make sure port 3000 is not used by another application
- **Network issues:** Ensure your firewall allows connections to localhost:3000

### QR Code Not Appearing

- **Check browser console:** Look for API call errors
- **Check bot logs:** Verify the bot is receiving initialization requests
- **Check salon ID:** Ensure you're using a valid salon ID from your database

### Messages Not Sending

- **Check authentication:** Ensure WhatsApp is properly connected
- **Check phone numbers:** Verify phone numbers are in correct format (10 digits)
- **Check bot logs:** Look for message sending errors in the terminal

## API Endpoints (Local)

When running locally, these endpoints will be available:

- `POST http://localhost:3000/session/{salonId}/init` - Initialize session
- `GET http://localhost:3000/session/{salonId}/qr` - Get QR code
- `GET http://localhost:3000/session/{salonId}/status` - Check status
- `POST http://localhost:3000/session/{salonId}/send` - Send message
- `POST http://localhost:3000/session/{salonId}/disconnect` - Disconnect
- `GET http://localhost:3000/health` - Health check

## Environment Variables Reference

### Main App (.env.local)
```
# Option 1: Use local bot flag (recommended)
VITE_USE_LOCAL_WHATSAPP=true

# Option 2: Direct URL override
VITE_WHATSAPP_API_URL=http://localhost:3000
```

### WhatsApp Bot (wbot/.env)
```
SUPABASE_URL=https://mixjfinrxzpplzqidlas.supabase.co
SUPABASE_SERVICE_KEY=your_service_key_here
PORT=3000
NODE_ENV=development
```

## Switching Back to Production

To switch back to production WhatsApp API:

1. **Stop the local bot** (Ctrl+C)
2. **Comment out or remove** both `VITE_USE_LOCAL_WHATSAPP` and `VITE_WHATSAPP_API_URL` from `.env.local`
3. **Restart the main app** (`npm run dev`)

The app will automatically use the production WhatsApp API URL (`https://wb-userbot-production.up.railway.app`).

## Notes

- **Local sessions are separate** from production sessions
- **Database is shared** between local and production (same Supabase instance)
- **Auth states are stored** in the database, so they persist between restarts
- **Multiple salons** can be tested simultaneously with the local bot
