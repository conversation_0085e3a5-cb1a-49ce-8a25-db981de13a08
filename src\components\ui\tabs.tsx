import * as React from "react";
import { cn } from "../../lib/utils";

interface TabsProps {
  children: React.ReactNode;
  className?: string;
}

export function Tabs({ children, className }: TabsProps) {
  return (
    <div className={cn("overflow-x-auto", className)}>
      <div className="flex space-x-1 border-b border-gray-200">{children}</div>
    </div>
  );
}

interface TabProps {
  children: React.ReactNode;
  active?: boolean;
  onClick?: () => void;
  className?: string;
}

export function Tab({ children, active, onClick, className }: TabProps) {
  return (
    <button
      className={cn(
        "px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors",
        active
          ? "border-b-2 border-indigo-600 text-indigo-600"
          : "text-gray-600 hover:text-indigo-600 hover:border-b-2 hover:border-indigo-300",
        className
      )}
      onClick={onClick}
    >
      {children}
    </button>
  );
}
