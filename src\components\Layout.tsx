import React from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { Navigation } from './Navigation';
import { MobileNavBar } from './MobileNavBar';
import { useAuth } from '../contexts/SimpleAuthContext';

export function Layout() {
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleLogout = async () => {
    await logout();
    navigate('/auth');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navigation onLogout={handleLogout} />
      <main className="flex-1 container mx-auto px-4 py-4 sm:py-6 md:py-8 overflow-x-hidden">
        <Outlet />
      </main>

      {/* Mobile bottom padding to ensure content isn't hidden behind bottom navigation on mobile */}
      <div className="h-16 md:hidden"></div>

      {/* Mobile bottom navigation */}
      <MobileNavBar />
    </div>
  );
}