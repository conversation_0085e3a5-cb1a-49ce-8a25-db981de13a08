import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

// Get WhatsApp API URL from environment or use Railway deployment
const API_BASE_URL = Deno.env.get("WHATSAPP_API_URL") ||
  "https://wb-userbot-production.up.railway.app";

console.log(`🔗 WhatsApp Queue Processor using API URL: ${API_BASE_URL}`);

interface QueuedMessage {
  id: string;
  salon_id: string;
  phone_number: string;
  message: string;
  message_type: string;
  appointment_id?: string;
  status: string;
  retry_count: number;
  max_retries: number;
  next_retry_at: string;
  created_at: string;
  error_message?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("🔄 WhatsApp queue processor started");

    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    // Get pending messages that are ready to be sent
    // Also include auth_required messages that might be retryable now
    const { data: pendingMessages, error: fetchError } = await supabaseClient
      .from("whatsapp_message_queue")
      .select("*")
      .in("status", ["pending", "auth_required"])
      .lte("next_retry_at", new Date().toISOString())
      .order("created_at", { ascending: true })
      .limit(20); // Increased limit to handle batching better

    if (fetchError) {
      console.error("❌ Error fetching pending messages:", fetchError);
      throw new Error(
        `Failed to fetch pending messages: ${fetchError.message}`,
      );
    }

    if (!pendingMessages || pendingMessages.length === 0) {
      console.log("📭 No pending messages to process");
      return new Response(
        JSON.stringify({
          message: "No pending messages to process",
          processed: 0,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 200,
        },
      );
    }

    console.log(
      `📨 Found ${pendingMessages.length} pending messages to process`,
    );

    // Group messages by phone number to implement smart batching
    const messagesByPhone = new Map<string, QueuedMessage[]>();
    pendingMessages.forEach((message) => {
      const key = `${message.phone_number}-${message.salon_id}`;
      if (!messagesByPhone.has(key)) {
        messagesByPhone.set(key, []);
      }
      messagesByPhone.get(key)!.push(message);
    });

    console.log(`📱 Grouped into ${messagesByPhone.size} unique phone numbers`);

    let successCount = 0;
    let failureCount = 0;
    let processedPhoneNumbers = 0;

    // Process messages grouped by phone number with smart batching
    for (const [phoneKey, messages] of messagesByPhone) {
      processedPhoneNumbers++;
      console.log(
        `📞 Processing ${messages.length} messages for ${phoneKey} (${processedPhoneNumbers}/${messagesByPhone.size})`,
      );

      // For multiple messages to same number, only send the latest one
      // and mark others as 'superseded'
      if (messages.length > 1) {
        console.log(
          `🔄 Found ${messages.length} messages for same recipient, using smart batching`,
        );

        // Sort by created_at to get the latest message
        messages.sort((a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        const latestMessage = messages[0];
        const olderMessages = messages.slice(1);

        // Mark older messages as superseded
        for (const oldMessage of olderMessages) {
          console.log(`⏭️ Superseding older message ${oldMessage.id}`);
          await supabaseClient
            .from("whatsapp_message_queue")
            .update({
              status: "superseded",
              error_message: "Superseded by newer message to same recipient",
              updated_at: new Date().toISOString(),
            })
            .eq("id", oldMessage.id);
        }

        // Process only the latest message
        messages.length = 1;
        messages[0] = latestMessage;
      }

      // Process the message(s) for this phone number
      for (const message of messages) {
        try {
          console.log(
            `📤 Processing message ${message.id} for salon ${message.salon_id}`,
          );

          // Check if salon has WhatsApp enabled
          const { data: salonData, error: salonError } = await supabaseClient
            .from("salons")
            .select("whatsapp_enabled")
            .eq("salon_id", message.salon_id)
            .single();

          if (salonError || !salonData?.whatsapp_enabled) {
            console.log(
              `⏭️ Skipping message ${message.id} - WhatsApp disabled for salon`,
            );

            // Mark as cancelled
            await supabaseClient
              .from("whatsapp_message_queue")
              .update({
                status: "cancelled",
                error_message: "WhatsApp disabled for salon",
                updated_at: new Date().toISOString(),
              })
              .eq("id", message.id);

            continue;
          }

          // Send WhatsApp message
          const response = await fetch(
            `${API_BASE_URL}/session/${message.salon_id}/send`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                phoneNumber: message.phone_number,
                message: message.message,
              }),
            },
          );

          const result = await response.json();

          if (response.ok) {
            // Message sent successfully
            console.log(`✅ Message ${message.id} sent successfully`);

            await supabaseClient
              .from("whatsapp_message_queue")
              .update({
                status: "sent",
                sent_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })
              .eq("id", message.id);

            successCount++;
          } else {
            // Message failed
            console.error(`❌ Message ${message.id} failed:`, result);

            // Check if this is an authentication error
            if (
              response.status === 403 ||
              (result.message && result.message.includes("not authenticated"))
            ) {
              console.log(
                `🔐 Authentication error for salon ${message.salon_id} - marking message as auth_required`,
              );

              // Mark as requiring authentication instead of retrying
              await supabaseClient
                .from("whatsapp_message_queue")
                .update({
                  status: "auth_required",
                  error_message:
                    "WhatsApp session not authenticated. Please scan QR code in settings.",
                  updated_at: new Date().toISOString(),
                })
                .eq("id", message.id);

              failureCount++;
              continue; // Skip retry logic for auth errors
            }

            const newRetryCount = message.retry_count + 1;
            const shouldRetry = newRetryCount < message.max_retries;

            if (shouldRetry) {
              // Schedule retry with exponential backoff
              const retryDelayMinutes = Math.pow(2, newRetryCount) * 5; // 5, 10, 20 minutes
              const nextRetryAt = new Date(
                Date.now() + retryDelayMinutes * 60 * 1000,
              );

              console.log(
                `🔄 Scheduling retry ${newRetryCount}/${message.max_retries} for message ${message.id} in ${retryDelayMinutes} minutes`,
              );

              await supabaseClient
                .from("whatsapp_message_queue")
                .update({
                  retry_count: newRetryCount,
                  next_retry_at: nextRetryAt.toISOString(),
                  error_message: result.message || `HTTP ${response.status}`,
                  updated_at: new Date().toISOString(),
                })
                .eq("id", message.id);
            } else {
              // Max retries reached, mark as failed
              console.log(
                `💀 Message ${message.id} failed permanently after ${message.max_retries} retries`,
              );

              await supabaseClient
                .from("whatsapp_message_queue")
                .update({
                  status: "failed",
                  retry_count: newRetryCount,
                  error_message: result.message || `HTTP ${response.status}`,
                  updated_at: new Date().toISOString(),
                })
                .eq("id", message.id);
            }

            failureCount++;
          }
        } catch (error) {
          console.error(`💥 Error processing message ${message.id}:`, error);
          failureCount++;

          // Update retry count and schedule retry
          const newRetryCount = message.retry_count + 1;
          const shouldRetry = newRetryCount < message.max_retries;

          if (shouldRetry) {
            const retryDelayMinutes = Math.pow(2, newRetryCount) * 5;
            const nextRetryAt = new Date(
              Date.now() + retryDelayMinutes * 60 * 1000,
            );

            await supabaseClient
              .from("whatsapp_message_queue")
              .update({
                retry_count: newRetryCount,
                next_retry_at: nextRetryAt.toISOString(),
                error_message: error.message,
                updated_at: new Date().toISOString(),
              })
              .eq("id", message.id);
          } else {
            await supabaseClient
              .from("whatsapp_message_queue")
              .update({
                status: "failed",
                retry_count: newRetryCount,
                error_message: error.message,
                updated_at: new Date().toISOString(),
              })
              .eq("id", message.id);
          }
        }

        // Add delay between phone numbers to avoid overwhelming WhatsApp
        if (processedPhoneNumbers < messagesByPhone.size) {
          console.log("⏳ Waiting 3 seconds before next phone number...");
          await new Promise((resolve) => setTimeout(resolve, 3000));
        }
      }
    }

    console.log(
      `🏁 Queue processing completed: ${successCount} sent, ${failureCount} failed`,
    );

    return new Response(
      JSON.stringify({
        success: true,
        message: "Queue processing completed",
        processed: pendingMessages.length,
        sent: successCount,
        failed: failureCount,
        unique_recipients: messagesByPhone.size,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("💥 Error in queue processor:", error);
    return new Response(
      JSON.stringify({
        error: "Queue processor error",
        details: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      },
    );
  }
});
