import React, { useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';

interface EditServiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (service: { name: string; duration: string; price: number; description?: string }) => void;
  service: {
    service_id: string;
    name: string;
    description: string | null;
    price: number;
    duration: number | null;
  };
}

export function EditServiceDialog({ isOpen, onClose, onSubmit, service }: EditServiceDialogProps) {
  const [name, setName] = React.useState(service.name);
  const [description, setDescription] = React.useState(service.description || '');
  const [duration, setDuration] = React.useState(service.duration?.toString() || '');
  const [price, setPrice] = React.useState(service.price.toString());

  // Update form when service changes
  useEffect(() => {
    setName(service.name);
    setDescription(service.description || '');
    setDuration(service.duration?.toString() || '');
    setPrice(service.price.toString());
  }, [service]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      name,
      duration,
      price: Number(price),
      description: description || undefined,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">Edit Service</h2>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Service Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Optional description"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Input
                id="duration"
                type="number"
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
                min="1"
                placeholder="Optional"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="price">Price (₹)</Label>
              <Input
                id="price"
                type="number"
                value={price}
                onChange={(e) => setPrice(e.target.value)}
                min="0"
                step="0.01"
                required
              />
            </div>
          </div>
          <div className="mt-6 flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </div>
    </div>
  );
}
