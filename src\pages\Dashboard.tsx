import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Users, Calendar, Scissors, TrendingUp, Clock, User, Loader2, IndianRupee } from 'lucide-react';
import { format, isToday, parseISO } from 'date-fns';
import { CardModern as Card, CardContent, CardHeader, CardTitle } from '../components/ui/card-modern';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { AppointmentDialog } from '../components/AppointmentDialog';
import { AppointmentStatus } from '../types/appointment';
import { Appointment, getAppointments, updateAppointmentStatus, addAppointment, getAppointment } from '../services/appointmentService';
import { getCurrentUserSalonId, getClients, getServices, getStaff } from '../services/salonService';
import { Client, Service, StaffMember } from '../services/salonService';
import { useToast } from '../components/ui/use-toast';
import { BookAppointmentDialog } from '../components/BookAppointmentDialogNew';

export function Dashboard() {
  const [selectedAppointment, setSelectedAppointment] = useState<string | null>(null);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [todayAppointments, setTodayAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [salonId, setSalonId] = useState<string | null>(null);
  const [showBookDialog, setShowBookDialog] = useState(false);
  // Removed unused state
  const [clients, setClients] = useState<Client[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const { toast } = useToast();

  // Fetch salon ID and appointments on component mount
  useEffect(() => {
    const fetchSalonId = async () => {
      try {
        const id = await getCurrentUserSalonId();
        setSalonId(id);
        if (id) {
          fetchAppointments(id);
          fetchClients(id);
          fetchServices(id);
          fetchStaff(id);
        } else {
          setLoading(false);
          toast({
            title: 'Error',
            description: 'Could not determine your salon. Please try again later.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching salon ID:', error);
        setLoading(false);
      }
    };

    fetchSalonId();
  }, []);

  const fetchClients = async (id: string) => {
    try {
      const data = await getClients(id);
      setClients(data);
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const fetchServices = async (id: string) => {
    try {
      const data = await getServices(id);
      setServices(data);
    } catch (error) {
      console.error('Error fetching services:', error);
    }
  };

  const fetchStaff = async (id: string) => {
    try {
      const data = await getStaff(id);
      setStaff(data);
    } catch (error) {
      console.error('Error fetching staff:', error);
    }
  };

  // Filter today's appointments
  useEffect(() => {
    // Use isToday function to filter appointments for today
    const filtered = appointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.appointment_date);
      return isToday(appointmentDate);
    });

    // Sort by appointment time (earliest first for today's appointments)
    filtered.sort((a, b) => {
      return new Date(a.appointment_date).getTime() - new Date(b.appointment_date).getTime();
    });

    setTodayAppointments(filtered);
  }, [appointments]);

  const fetchAppointments = async (id: string) => {
    setLoading(true);
    try {
      const data = await getAppointments(id);
      setAppointments(data);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load appointments. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBookAppointment = async (appointmentData: {
    clientId: string;
    serviceIds: string[];
    staffId: string;
    appointmentDate: string;
  }) => {
    if (!salonId) {
      toast({
        title: 'Error',
        description: 'Could not determine your salon. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newAppointment = await addAppointment({
        salon_id: salonId,
        client_id: appointmentData.clientId,
        service_ids: appointmentData.serviceIds,
        staff_id: appointmentData.staffId,
        appointment_date: appointmentData.appointmentDate,
        status: 'scheduled',
      });

      if (newAppointment) {
        // Find the client and staff details
        const client = clients.find(c => c.client_id === appointmentData.clientId);
        const staffMember = staff.find(s => s.user_id === appointmentData.staffId);

        // Get all selected services
        const selectedServices = services.filter(s =>
          appointmentData.serviceIds.includes(s.service_id)
        );

        const appointmentWithDetails = {
          ...newAppointment,
          client_name: client ? `${client.first_name} ${client.last_name}` : '',
          service_name: selectedServices.map(s => s.name).join(', '),
          staff_name: staffMember ? `${staffMember.first_name} ${staffMember.last_name}` : '',
          price: newAppointment.total_price || selectedServices.reduce((sum, s) => sum + s.price, 0),
        };

        // Add new appointment at the beginning of the array to show it at the top
        setAppointments([appointmentWithDetails, ...appointments]);
        toast({
          title: 'Success',
          description: 'Appointment booked successfully',
        });
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
      toast({
        title: 'Error',
        description: 'Failed to book appointment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const quickActions = [
    {
      name: 'New Appointment',
      icon: Plus,
      action: () => setShowBookDialog(true),
      color: 'bg-indigo-600'
    },
    {
      name: 'Add Client',
      icon: Users,
      href: '/clients',
      color: 'bg-emerald-600'
    },
    {
      name: 'Add Service',
      icon: Scissors,
      href: '/services',
      color: 'bg-purple-600'
    },
  ];

  // Calculate stats based on today's appointments
  const calculateStats = () => {
    // Calculate today's revenue from completed appointments
    const todayRevenue = todayAppointments
      .filter(appointment => appointment.status === 'completed')
      .reduce((sum, appointment) => sum + (appointment.price || 0), 0);

    // Count of today's appointments
    const todayCount = todayAppointments.length;

    // Count of upcoming appointments (scheduled or checked-in)
    const upcomingCount = todayAppointments.filter(
      appointment => appointment.status === 'scheduled' || appointment.status === 'checked-in'
    ).length;

    return [
      {
        name: "Today's Revenue",
        value: `₹${todayRevenue.toFixed(2)}`,
        icon: IndianRupee,
        color: 'text-emerald-600',
        bgColor: 'bg-emerald-50',
        borderColor: 'border-emerald-100'
      },
      {
        name: "Today's Appointments",
        value: todayCount.toString(),
        icon: Calendar,
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        borderColor: 'border-indigo-100'
      },
      {
        name: 'Upcoming',
        value: upcomingCount.toString(),
        icon: Clock,
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-100'
      },
    ];
  };

  const stats = calculateStats();

  const handleStatusChange = async (appointmentId: string, newStatus: AppointmentStatus) => {
    try {
      const updatedAppointment = await updateAppointmentStatus(appointmentId, newStatus);
      if (updatedAppointment) {
        // Update the appointments list
        const updatedAppointments = appointments.map(appointment =>
          appointment.appointment_id === appointmentId ? { ...appointment, status: newStatus } : appointment
        );
        setAppointments(updatedAppointments);

        // If we have a selected appointment and it's the one being updated, update the selectedAppointment state
        if (selectedAppointment === appointmentId) {
          // We don't need to update the selectedAppointment state here because we're using the updated appointment
          // from the appointments list in the selectedAppointmentData computed value
        }

        toast({
          title: 'Success',
          description: `Appointment ${newStatus === 'checked-in' ? 'checked in' : newStatus} successfully`,
        });

        // Close the dialog if the appointment was cancelled or completed
        if (newStatus === 'cancelled' || newStatus === 'completed') {
          setSelectedAppointment(null);
        }
      }
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update appointment status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleReschedule = (_appointmentId: string) => {
    // We don't need to do anything here since the rescheduling is handled in the RescheduleAppointmentDialog
    // Just refresh the appointments list to show the updated appointment
    if (salonId) {
      fetchAppointments(salonId);
    }
  };

  const handleCancel = async (appointmentId: string) => {
    try {
      await handleStatusChange(appointmentId, 'cancelled');
    } catch (error) {
      console.error('Error cancelling appointment:', error);
    }
  };

  const selectedAppointmentData = todayAppointments.find(a => a.appointment_id === selectedAppointment);

  return (
    <div className="space-y-6">
      {/* Stats */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card
              key={stat.name}
              gradient
              hover
              className={`${stat.borderColor}`}
            >
              <CardContent className="p-5">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      {stat.name}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className={`h-12 w-12 rounded-full ${stat.bgColor} flex items-center justify-center ${stat.color}`}>
                    <Icon className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
        {quickActions.map((action) => {
          const Icon = action.icon;

          // Define gradient colors based on the action color
          const gradientClass = action.color === 'bg-indigo-600'
            ? 'bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600'
            : action.color === 'bg-emerald-600'
              ? 'bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-500 hover:to-teal-500'
              : 'bg-gradient-to-r from-purple-600 to-fuchsia-600 hover:from-purple-500 hover:to-fuchsia-500';

          // If action has an action property, render a button
          if (action.action) {
            return (
              <div key={action.name} className="w-full">
                <Button
                  variant="default"
                  className={`w-full justify-start h-14 text-white shadow-sm hover:shadow-md transition-all ${gradientClass} rounded-xl`}
                  onClick={action.action}
                >
                  <div className="flex items-center justify-center bg-white/20 rounded-full h-9 w-9 mr-3">
                    <Icon className="h-5 w-5" />
                  </div>
                  <span className="text-sm font-medium">{action.name}</span>
                </Button>
              </div>
            );
          }

          // Otherwise, render a link
          return (
            <Link key={action.name} to={action.href || '#'} className="w-full">
              <Button
                variant="default"
                className={`w-full justify-start h-14 text-white shadow-sm hover:shadow-md transition-all ${gradientClass} rounded-xl`}
              >
                <div className="flex items-center justify-center bg-white/20 rounded-full h-9 w-9 mr-3">
                  <Icon className="h-5 w-5" />
                </div>
                <span className="text-sm font-medium">{action.name}</span>
              </Button>
            </Link>
          );
        })}
      </div>

      {/* Today's Schedule */}
      <Card gradient hover>
        <CardHeader className="border-b border-gray-100">
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-indigo-600" />
            Today's Schedule
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
          ) : todayAppointments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p>No appointments scheduled for today.</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => setShowBookDialog(true)}
              >
                <Plus className="h-4 w-4 mr-1" />
                Book Appointment
              </Button>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {todayAppointments.map((appointment) => {
                const appointmentTime = new Date(appointment.appointment_date);
                const statusVariants = {
                  'scheduled': 'bg-blue-50 text-blue-700 border-blue-100',
                  'checked-in': 'bg-amber-50 text-amber-700 border-amber-100',
                  'completed': 'bg-green-50 text-green-700 border-green-100',
                  'cancelled': 'bg-red-50 text-red-700 border-red-100',
                };

                return (
                  <div
                    key={appointment.appointment_id}
                    className="p-4 cursor-pointer hover:bg-gray-50/50 transition-colors"
                    onClick={async () => {
                      // Fetch the latest appointment data before showing the dialog
                      try {
                        console.log('Dashboard: Fetching appointment details for:', appointment.appointment_id);
                        const latestAppointment = await getAppointment(appointment.appointment_id);
                        console.log('Dashboard: Latest appointment data received:', latestAppointment);

                        if (latestAppointment) {
                          console.log('Dashboard: Payment method in fetched appointment:', latestAppointment.paymentMethod);

                          // Update the appointment in the list
                          setAppointments(appointments.map(a =>
                            a.appointment_id === latestAppointment.appointment_id ? latestAppointment : a
                          ));
                        }
                      } catch (error) {
                        console.error('Error fetching appointment details:', error);
                      }
                      setSelectedAppointment(appointment.appointment_id);
                    }}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 flex-shrink-0">
                          <User className="h-5 w-5" />
                        </div>
                        <div>
                          <div className="flex flex-wrap items-center gap-2">
                            <span className="font-medium text-gray-900">{appointment.client_name}</span>
                            <span className={`text-xs px-2 py-0.5 rounded-full border ${statusVariants[appointment.status]}`}>
                              {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-0.5">{appointment.service_name}</p>
                          <div className="flex flex-wrap items-center text-xs text-gray-500 mt-1 gap-x-3 gap-y-1">
                            <div className="flex items-center">
                              <Clock className="mr-1 h-3.5 w-3.5" />
                              {format(appointmentTime, 'h:mm a')}
                            </div>
                            <div className="flex items-center">
                              <User className="mr-1 h-3.5 w-3.5" />
                              {appointment.staff_name}
                            </div>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="rounded-full h-8 w-8 p-0 flex items-center justify-center text-indigo-600"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {selectedAppointmentData && (
        <AppointmentDialog
          isOpen={selectedAppointment !== null}
          onClose={() => setSelectedAppointment(null)}
          appointment={{
            id: selectedAppointmentData.appointment_id,
            client: selectedAppointmentData.client_name || '',
            services: selectedAppointmentData.services || [],  // Pass the services array
            appointmentDate: selectedAppointmentData.appointment_date,
            stylist: selectedAppointmentData.staff_name || '',
            status: selectedAppointmentData.status,
            price: selectedAppointmentData.price || 0,
            paymentMethod: selectedAppointmentData.paymentMethod, // Include the payment method
          }}
          onStatusChange={(status) => handleStatusChange(selectedAppointmentData.appointment_id, status)}
          onReschedule={() => handleReschedule(selectedAppointmentData.appointment_id)}
          onCancel={() => handleCancel(selectedAppointmentData.appointment_id)}
          staffMembers={staff.map(s => ({
            id: s.user_id,
            name: `${s.first_name} ${s.last_name}`
          }))}
          services={services.map(s => ({
            id: s.service_id,
            name: s.name,
            price: s.price
          }))}
        />
      )}

      {/* Book Appointment Dialog */}
      <BookAppointmentDialog
        isOpen={showBookDialog}
        onClose={() => setShowBookDialog(false)}
        clients={clients}
        services={services}
        staff={staff}
        onSubmit={handleBookAppointment}
      />
    </div>
  );
}
