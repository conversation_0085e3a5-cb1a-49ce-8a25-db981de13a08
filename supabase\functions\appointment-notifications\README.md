# WhatsApp Appointment Notifications Edge Function

This Supabase Edge Function sends WhatsApp notifications to clients when appointments are created, rescheduled, canceled, or checked in/out.

## Deployment Instructions

### 1. Prerequisites

- Supabase CLI installed
- Access to your Supabase project
- WhatsApp API service running at the specified URL

### 2. Deploy the Edge Function

```bash
# Login to Supabase CLI
supabase login

# Link your project (if not already linked)
supabase link --project-ref your-project-ref

# Deploy the function
supabase functions deploy appointment-notifications --no-verify-jwt
```

### 3. Set Up Database Trigger

After deploying the function, you need to create a database trigger to call it when appointments are created or updated.

1. Go to your Supabase dashboard
2. Navigate to Database > Triggers
3. Click "Create a new trigger"
4. Configure the trigger with the following settings:

```sql
-- Create the trigger function
CREATE OR REPLACE FUNCTION public.handle_appointment_changes()
RETURNS TRIGGER AS $$
BEGIN
  -- Make a request to the edge function
  PERFORM
    net.http_post(
      'https://<your-project-ref>.supabase.co/functions/v1/appointment-notifications',
      jsonb_build_object(
        'type', TG_OP,
        'table', TG_TABLE_NAME,
        'record', row_to_json(NEW),
        'old_record', CASE WHEN TG_OP = 'UPDATE' THEN row_to_json(OLD) ELSE null END
      ),
      jsonb_build_object('Content-Type', 'application/json', 'Authorization', 'Bearer ' || current_setting('request.jwt.claim.sub', true))
    );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
DROP TRIGGER IF EXISTS on_appointment_changes ON public.appointments;
CREATE TRIGGER on_appointment_changes
  AFTER INSERT OR UPDATE
  ON public.appointments
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_appointment_changes();
```

5. Replace `<your-project-ref>` with your actual Supabase project reference

### 4. Add WhatsApp Enabled Column to Salons Table

You need to add a `whatsapp_enabled` column to the `salons` table:

```sql
ALTER TABLE public.salons
ADD COLUMN IF NOT EXISTS whatsapp_enabled BOOLEAN DEFAULT false;
```

## Testing

To test the function:

1. Enable WhatsApp in the settings page for a salon
2. Connect WhatsApp by scanning the QR code
3. Create, reschedule, or update an appointment
4. Check the client's WhatsApp for notifications

## Troubleshooting

### Common Issues

1. **Function not being triggered**: Check that the database trigger is properly set up and active.

2. **Authentication errors**: If you see 403 errors, the WhatsApp session may need to be reconnected. Have the salon owner scan the QR code again in the settings page.

3. **Missing client phone numbers**: Ensure client phone numbers are properly formatted (10 digits). The function will attempt to add the country code '91' if missing.

4. **Function logs**: Check the Supabase Edge Function logs for detailed error information:
   ```bash
   supabase functions logs appointment-notifications
   ```

### WhatsApp Connection Issues

If WhatsApp notifications aren't being sent:

1. Check if WhatsApp is enabled for the salon in the database
2. Verify the WhatsApp session is authenticated
3. Ensure the client's phone number is valid
4. Check the Edge Function logs for any errors

## Message Templates

The function uses the following message templates:

### Appointment Created
```
*Appointment Confirmation*

Hi {client_name},

Your appointment at {salon_name} has been confirmed for {date} at {time}.

*Services:*
- {service_name}: ₹{price}

*Total: ₹{total_price}*

*Staff: {staff_name}*

*Location:* {salon_address}

We look forward to seeing you!

_Powered by Salon Orbit Lite_
```

### Appointment Cancelled
```
*Appointment Cancelled*

Hi {client_name},

Your appointment at {salon_name} for {date} at {time} has been cancelled.

If you would like to reschedule, please contact us.

_Powered by Salon Orbit Lite_
```

### Appointment Rescheduled
```
*Appointment Rescheduled*

Hi {client_name},

Your appointment at {salon_name} has been rescheduled.

*Previous:* {old_date} at {old_time}
*New:* {new_date} at {new_time}

*Services:*
- {service_name}: ₹{price}

*Total: ₹{total_price}*

*Staff: {staff_name}*

If you need to make any changes, please contact us.

_Powered by Salon Orbit Lite_
```

### Appointment Check-in
```
*Appointment Check-in*

Hi {client_name},

You have been checked in for your appointment at {salon_name}.

*Services:*
- {service_name}: ₹{price}

*Total: ₹{total_price}*

Thank you for your visit!

_Powered by Salon Orbit Lite_
```

### Appointment Completed
```
*Appointment Completed*

Hi {client_name},

Thank you for visiting {salon_name}!

*Services:*
- {service_name}: ₹{price}

*Total: ₹{total_price}*

We hope you enjoyed your experience and look forward to seeing you again soon!

_Powered by Salon Orbit Lite_
```
