import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit2, Trash2, Loader2, <PERSON><PERSON><PERSON><PERSON>, Sciss<PERSON>, Clock, IndianRupee } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { AddServiceDialog } from '../components/AddServiceDialog';
import { EditServiceDialog } from '../components/EditServiceDialog';
import { Service, getServices, addService, deleteService, updateService, deactivateService, activateService, getCurrentUserSalonId } from '../services/salonService';
import { useToast } from '../components/ui/use-toast';
import { Pagination } from '../components/ui/pagination';
import { CardModern as Card, CardContent, CardHeader, CardTitle } from '../components/ui/card-modern';
import { MobileServicesList } from '../components/mobile/MobileServicesList';

export function Services() {
  const [services, setServices] = useState<Service[]>([]);
  const [filteredServices, setFilteredServices] = useState<Service[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);
  const [salonId, setSalonId] = useState<string | null>(null);
  const [showInactive, setShowInactive] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { toast } = useToast();

  // Fetch salon ID and services on component mount
  useEffect(() => {
    const fetchSalonId = async () => {
      try {
        const id = await getCurrentUserSalonId();
        setSalonId(id);
        if (id) {
          fetchServices(id);
        } else {
          setLoading(false);
          toast({
            title: 'Error',
            description: 'Could not determine your salon. Please try again later.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching salon ID:', error);
        setLoading(false);
      }
    };

    fetchSalonId();
  }, []);

  // Filter services when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredServices(services);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredServices(
        services.filter(
          (service) =>
            service.name.toLowerCase().includes(query) ||
            (service.description && service.description.toLowerCase().includes(query))
        )
      );
    }
    // Reset to first page when search query changes
    setCurrentPage(1);
  }, [searchQuery, services]);

  // Refetch services when showInactive changes
  useEffect(() => {
    if (salonId) {
      fetchServices(salonId);
    }
  }, [showInactive]);

  const fetchServices = async (id: string) => {
    setLoading(true);
    try {
      const data = await getServices(id, showInactive);
      setServices(data);
      setFilteredServices(data);
    } catch (error) {
      console.error('Error fetching services:', error);
      toast({
        title: 'Error',
        description: 'Failed to load services. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddService = async (serviceData: { name: string; duration: string; price: number; description?: string }) => {
    if (!salonId) {
      toast({
        title: 'Error',
        description: 'Could not determine your salon. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Handle optional duration field
      const duration = serviceData.duration ? parseInt(serviceData.duration) : null;

      const newService = await addService({
        salon_id: salonId,
        name: serviceData.name,
        description: serviceData.description || '',
        price: serviceData.price,
        duration: duration,
      });

      if (newService) {
        setServices([...services, newService]);
        toast({
          title: 'Success',
          description: 'Service added successfully',
        });
      }
    } catch (error) {
      console.error('Error adding service:', error);
      toast({
        title: 'Error',
        description: 'Failed to add service. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleEditService = async (serviceData: {
    name: string;
    duration: string;
    price: number;
    description?: string
  }) => {
    if (!selectedService) return;

    try {
      // Handle optional duration field
      const duration = serviceData.duration ? parseInt(serviceData.duration) : null;

      const updatedService = await updateService(selectedService.service_id, {
        name: serviceData.name,
        description: serviceData.description || null,
        price: serviceData.price,
        duration: duration,
      });

      if (updatedService) {
        // Update the services list with the updated service
        setServices(services.map(service =>
          service.service_id === updatedService.service_id ? updatedService : service
        ));
        toast({
          title: 'Success',
          description: 'Service updated successfully',
        });
      }
    } catch (error) {
      console.error('Error updating service:', error);
      toast({
        title: 'Error',
        description: 'Failed to update service. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeactivateService = async (serviceId: string) => {
    if (confirm('Are you sure you want to deactivate this service? It will no longer appear in selection lists but can be reactivated later.')) {
      try {
        const deactivatedService = await deactivateService(serviceId);
        if (deactivatedService) {
          // If we're not showing inactive services, remove it from the list
          if (!showInactive) {
            setServices(services.filter(service => service.service_id !== serviceId));
          } else {
            // Otherwise, update the service in the list
            setServices(services.map(service =>
              service.service_id === serviceId ? deactivatedService : service
            ));
          }
          toast({
            title: 'Success',
            description: 'Service deactivated successfully',
          });
        }
      } catch (error) {
        console.error('Error deactivating service:', error);
        toast({
          title: 'Error',
          description: 'Failed to deactivate service. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleActivateService = async (serviceId: string) => {
    try {
      const activatedService = await activateService(serviceId);
      if (activatedService) {
        setServices(services.map(service =>
          service.service_id === serviceId ? activatedService : service
        ));
        toast({
          title: 'Success',
          description: 'Service activated successfully',
        });
      }
    } catch (error) {
      console.error('Error activating service:', error);
      toast({
        title: 'Error',
        description: 'Failed to activate service. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Keep for backward compatibility
  const handleDeleteService = handleDeactivateService;

  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Listen for the custom event to open the add service dialog
    const handleOpenServiceDialog = () => {
      setShowAddDialog(true);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('open-service-dialog', handleOpenServiceDialog);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('open-service-dialog', handleOpenServiceDialog);
    };
  }, []);

  return (
    <div className="space-y-6">
      {isMobile ? (
        // Mobile view
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-indigo-500" />
              <Input
                placeholder="Search services..."
                className="pl-10 rounded-full border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 shadow-sm bg-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center px-2 py-2 bg-white/80 rounded-lg shadow-sm border border-indigo-50">
            <input
              type="checkbox"
              id="showInactive"
              checked={showInactive}
              onChange={(e) => setShowInactive(e.target.checked)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label htmlFor="showInactive" className="text-sm text-gray-700 ml-2">
              Show inactive services
            </label>
          </div>
        </div>
      ) : (
        // Desktop view
        <Card gradient hover>
          <CardHeader className="flex flex-col sm:flex-row justify-between items-center">
            <CardTitle className="flex items-center">
              <Scissors className="h-5 w-5 mr-2 text-indigo-600" />
              Services
            </CardTitle>
            <Button
              onClick={() => setShowAddDialog(true)}
              className="mt-4 sm:mt-0 bg-indigo-600 hover:bg-indigo-700 rounded-full shadow-sm"
            >
              <Plus className="h-5 w-5 mr-2" />
              Add Service
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Search services..."
                  className="pl-10 rounded-lg border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2 bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                <input
                  type="checkbox"
                  id="showInactive"
                  checked={showInactive}
                  onChange={(e) => setShowInactive(e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="showInactive" className="text-sm text-gray-700">
                  Show inactive services
                </label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {isMobile ? (
        // Mobile view of services list
        <div className="rounded-xl overflow-hidden shadow-sm border border-indigo-50">
          <MobileServicesList
            services={filteredServices.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)}
            onEdit={(service) => {
              setSelectedService(service);
              setShowEditDialog(true);
            }}
            onActivate={handleActivateService}
            onDeactivate={handleDeactivateService}
            loading={loading}
            searchQuery={searchQuery}
          />

          {/* Pagination for mobile */}
          {!loading && filteredServices.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredServices.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                totalItems={filteredServices.length}
                itemsPerPage={itemsPerPage}
              />
            </div>
          )}
        </div>
      ) : (
        // Desktop view
        loading ? (
          <Card gradient className="py-10">
            <div className="flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
          </Card>
        ) : filteredServices.length === 0 ? (
          <Card gradient hover className="p-10 text-center">
            <div className="flex flex-col items-center">
              <Scissors className="h-12 w-12 text-gray-300 mb-4" />
              <p className="text-gray-500 mb-4">
                {searchQuery ? 'No services match your search' : 'No services found. Add your first service!'}
              </p>
              {!searchQuery && (
                <Button
                  onClick={() => setShowAddDialog(true)}
                  variant="outline"
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Service
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <Card gradient hover>
            <div className="divide-y divide-gray-100">
              {/* Get current services for pagination */}
              {filteredServices
                .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                .map((service) => {
                  // @ts-ignore - is_active property exists in the API response but not in the type
                  const isInactive = service.is_active === false;

                  return (
                    <div
                      key={service.service_id}
                      className={`p-4 hover:bg-gray-50/50 transition-colors ${isInactive ? 'opacity-75 bg-gray-50/50' : ''}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 flex-shrink-0">
                            <Scissors className="h-5 w-5" />
                          </div>
                          <div>
                            <div className="flex flex-wrap items-center gap-2">
                              <h3 className="text-sm font-medium text-gray-900">
                                {service.name}
                              </h3>
                              {isInactive && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                                  Inactive
                                </span>
                              )}
                            </div>

                            {service.description && (
                              <p className="text-sm text-gray-600 mt-0.5 line-clamp-2">
                                {service.description}
                              </p>
                            )}

                            <div className="flex flex-wrap items-center text-xs text-gray-500 mt-1 gap-x-3 gap-y-1">
                              {service.duration && (
                                <div className="flex items-center">
                                  <Clock className="mr-1 h-3.5 w-3.5 text-gray-400" />
                                  {service.duration} min
                                </div>
                              )}
                              <div className="flex items-center font-medium text-gray-900">
                                <IndianRupee className="mr-1 h-3.5 w-3.5 text-gray-400" />
                                {service.price}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full"
                            onClick={() => {
                              setSelectedService(service);
                              setShowEditDialog(true);
                            }}
                          >
                            <Edit2 className="h-4 w-4 text-gray-500" />
                          </Button>
                          {isInactive ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 rounded-full"
                              onClick={() => handleActivateService(service.service_id)}
                              title="Activate service"
                            >
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 rounded-full"
                              onClick={() => handleDeactivateService(service.service_id)}
                              title="Deactivate service"
                            >
                              <Trash2 className="h-4 w-4 text-red-400" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>

            {/* Pagination */}
            {filteredServices.length > 0 && (
              <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(filteredServices.length / itemsPerPage)}
                  onPageChange={setCurrentPage}
                  totalItems={filteredServices.length}
                  itemsPerPage={itemsPerPage}
                />
              </div>
            )}
          </Card>
        )
      )}

      <AddServiceDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={handleAddService}
      />

      {selectedService && (
        <EditServiceDialog
          isOpen={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          onSubmit={handleEditService}
          service={selectedService}
        />
      )}
    </div>
  );
}
