# Salon POS - Technical Context

## Core Technologies
- React 18 with TypeScript
- Vite build tool
- Tai<PERSON><PERSON> CSS for styling
- Radix UI for accessible components
- date-fns for date handling
- Lucide React for icons

## Development Setup
- Node.js environment
- ESLint for code quality
- Prettier for code formatting
- Vite dev server for local development

## Project Structure
- src/
  - components/: Reusable UI components
  - pages/: Main application pages
  - types/: TypeScript type definitions
  - lib/: Utility functions
