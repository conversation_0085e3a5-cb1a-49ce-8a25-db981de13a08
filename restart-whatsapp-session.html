<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Session Restart</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #25d366;
        text-align: center;
      }
      button {
        background-color: #25d366;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      button:hover {
        background-color: #128c7e;
      }
      button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
      .result {
        margin-top: 20px;
        padding: 15px;
        border-radius: 4px;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 14px;
      }
      .success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
      }
      .error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
      }
      .info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
      }
      .warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
      }
      .status-box {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        border: 1px solid #ddd;
      }
      .authenticated {
        background-color: #d4edda;
        border-color: #c3e6cb;
      }
      .not-authenticated {
        background-color: #f8d7da;
        border-color: #f5c6cb;
      }
    </style>
  </head>
  <body>
    <h1>🔄 WhatsApp Session Restart Tool</h1>

    <div class="container">
      <h2>Session Status</h2>
      <p>Current WhatsApp session status for your salon:</p>

      <div id="statusBox" class="status-box">
        <strong>Status:</strong> <span id="statusText">Checking...</span><br />
        <strong>WebSocket:</strong> <span id="websocketText">Unknown</span
        ><br />
        <strong>Last Check:</strong> <span id="lastCheckText">Never</span>
      </div>

      <button onclick="checkStatus()" id="checkBtn">🔍 Check Status</button>
      <button onclick="restartSession()" id="restartBtn">
        🔄 Restart Session
      </button>
      <button onclick="disconnectSession()" id="disconnectBtn">
        🔌 Disconnect Session
      </button>
      <button onclick="getQRCode()" id="qrBtn">📱 Get QR Code</button>

      <div id="result" class="result" style="display: none"></div>
    </div>

    <div class="container">
      <h2>What This Tool Does</h2>
      <div class="info result">
        <strong>🔄 Session Restart:</strong>
        - Forces the WhatsApp bot to close and reinitialize your session -
        Useful when WebSocket connection is undefined or stuck - Automatically
        loads your saved authentication data - Generates new QR code if
        authentication is needed

        <strong>🔍 Status Check:</strong>
        - Shows current connection status - Displays WebSocket state (should be
        1 for OPEN) - Indicates if authentication is working

        <strong>📱 QR Code:</strong>
        - Gets the current QR code if available - Use this to authenticate your
        WhatsApp connection - Scan with your phone's WhatsApp app

        <strong>When to Use:</strong>
        - Messages failing with "WebSocket is undefined" error - Session shows
        as authenticated but can't send messages - After bot restarts or
        deployments - When connection seems stuck
      </div>
    </div>

    <script>
      const SALON_ID = "d0a56955-eba7-4679-b0de-c9946505c1af";
      const BOT_URL = "https://wb-userbot-production.up.railway.app";

      async function checkStatus() {
        const statusText = document.getElementById("statusText");
        const websocketText = document.getElementById("websocketText");
        const lastCheckText = document.getElementById("lastCheckText");
        const resultDiv = document.getElementById("result");
        const statusBox = document.getElementById("statusBox");

        statusText.textContent = "Checking...";
        websocketText.textContent = "Checking...";

        try {
          const response = await fetch(`${BOT_URL}/session/${SALON_ID}/status`);
          const result = await response.json();

          const isAuthenticated = result.authenticated || false;
          const wsState = result.websocket_state;

          statusText.textContent = isAuthenticated
            ? "✅ Authenticated"
            : "❌ Not Authenticated";
          websocketText.textContent =
            wsState !== undefined
              ? `${wsState} (${getWSStateText(wsState)})`
              : "❌ Undefined";
          lastCheckText.textContent = new Date().toLocaleTimeString();

          // Update status box styling
          statusBox.className = `status-box ${
            isAuthenticated ? "authenticated" : "not-authenticated"
          }`;

          showResult(
            `Status Check Result:\n\n${JSON.stringify(result, null, 2)}`,
            isAuthenticated ? "success" : "warning"
          );
        } catch (error) {
          statusText.textContent = "❌ Error";
          websocketText.textContent = "❌ Error";
          lastCheckText.textContent = new Date().toLocaleTimeString();
          statusBox.className = "status-box not-authenticated";
          showResult(`Error checking status: ${error.message}`, "error");
        }
      }

      async function restartSession() {
        const restartBtn = document.getElementById("restartBtn");
        restartBtn.disabled = true;
        restartBtn.textContent = "🔄 Restarting...";

        try {
          showResult("Restarting WhatsApp session...", "info");

          const response = await fetch(
            `${BOT_URL}/session/${SALON_ID}/restart`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
          const result = await response.json();

          if (response.ok && result.success) {
            showResult(
              `✅ Session restarted successfully!\n\nAuthenticated: ${
                result.authenticated
              }\nQR Available: ${result.qr_available}\n\n${JSON.stringify(
                result,
                null,
                2
              )}`,
              "success"
            );

            // Auto-check status after restart
            setTimeout(checkStatus, 2000);
          } else {
            showResult(
              `❌ Failed to restart session:\n\n${JSON.stringify(
                result,
                null,
                2
              )}`,
              "error"
            );
          }
        } catch (error) {
          showResult(`❌ Error restarting session: ${error.message}`, "error");
        } finally {
          restartBtn.disabled = false;
          restartBtn.textContent = "🔄 Restart Session";
        }
      }

      async function disconnectSession() {
        const disconnectBtn = document.getElementById("disconnectBtn");
        disconnectBtn.disabled = true;
        disconnectBtn.textContent = "🔌 Disconnecting...";

        try {
          showResult(
            "Disconnecting WhatsApp session and clearing all auth data...",
            "info"
          );

          const response = await fetch(
            `${BOT_URL}/session/${SALON_ID}/disconnect`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
          const result = await response.json();

          if (response.ok && result.success) {
            showResult(
              `✅ Session disconnected successfully!\n\nAuth data cleared from database and local storage.\nNext connection will require fresh QR scan.\n\n${JSON.stringify(
                result,
                null,
                2
              )}`,
              "success"
            );

            // Auto-check status after disconnect
            setTimeout(checkStatus, 2000);
          } else {
            showResult(
              `❌ Failed to disconnect session:\n\n${JSON.stringify(
                result,
                null,
                2
              )}`,
              "error"
            );
          }
        } catch (error) {
          showResult(
            `❌ Error disconnecting session: ${error.message}`,
            "error"
          );
        } finally {
          disconnectBtn.disabled = false;
          disconnectBtn.textContent = "🔌 Disconnect Session";
        }
      }

      async function getQRCode() {
        const qrBtn = document.getElementById("qrBtn");
        qrBtn.disabled = true;
        qrBtn.textContent = "📱 Getting QR...";

        try {
          const response = await fetch(`${BOT_URL}/session/${SALON_ID}/qr`);
          const result = await response.json();

          if (response.ok && result.qr_image_url) {
            showResult(
              `📱 QR Code Available!\n\nScan this QR code with your WhatsApp:\n${
                result.qr_image_url
              }\n\n${JSON.stringify(result, null, 2)}`,
              "success"
            );
          } else {
            showResult(
              `❌ No QR code available:\n\n${JSON.stringify(result, null, 2)}`,
              "warning"
            );
          }
        } catch (error) {
          showResult(`❌ Error getting QR code: ${error.message}`, "error");
        } finally {
          qrBtn.disabled = false;
          qrBtn.textContent = "📱 Get QR Code";
        }
      }

      function getWSStateText(state) {
        switch (state) {
          case 0:
            return "CONNECTING";
          case 1:
            return "OPEN";
          case 2:
            return "CLOSING";
          case 3:
            return "CLOSED";
          default:
            return "UNKNOWN";
        }
      }

      function showResult(message, type) {
        const resultDiv = document.getElementById("result");
        resultDiv.style.display = "block";
        resultDiv.className = `result ${type}`;
        resultDiv.textContent = message;
      }

      // Auto-check status on page load
      window.onload = function () {
        checkStatus();
      };
    </script>
  </body>
</html>
