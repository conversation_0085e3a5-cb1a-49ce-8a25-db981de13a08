import React, { useState } from 'react';
import { Plus, Minus, Trash2, <PERSON><PERSON><PERSON>cle, ArrowLeft, ArrowRight, Receipt, CreditCard, IndianRupee } from 'lucide-react';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Label } from './ui/label';
import { Separator } from './ui/separator';

interface Service {
  id: number;
  name: string;
  price: number;
  quantity: number;
  originalId?: string; // Original service ID from the database
}

interface Staff {
  id: number;
  originalId?: string; // Original ID from the database
  name: string;
}

interface CheckoutDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (updatedServices: Service[], selectedStaffMap: { [key: number]: number }, paymentMethod: string) => void;
  initialServices: Service[];
  availableServices: Service[];
  availableStaff: Staff[];
}

type CheckoutStep = 'services' | 'summary' | 'success';

export function CheckoutDialog({
  isOpen,
  onClose,
  onComplete,
  initialServices,
  availableServices,
  availableStaff,
}: CheckoutDialogProps) {
  const [services, setServices] = useState<Service[]>(initialServices);
  const [selectedStaff, setSelectedStaff] = useState<{ [key: number]: number }>({});
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('services');
  const [paymentMethod, setPaymentMethod] = useState<string>('card');
  const [validationError, setValidationError] = useState<string>('');

  const addService = (service: Service) => {
    setServices(prev => {
      // First check if this service is already in our list
      const existing = prev.find(s => s.id === service.id && s.name === service.name);
      if (existing) {
        // If it exists, just increment the quantity
        return prev.map(s =>
          s.id === service.id && s.name === service.name ? { ...s, quantity: s.quantity + 1 } : s
        );
      }

      // If it's not in our list, add it with a unique ID
      // Find the highest ID currently in use
      const maxId = Math.max(...prev.map(s => s.id), 0);

      // Log the service being added
      console.log('Adding new service:', service);

      // Add the new service with an ID that's guaranteed to be unique
      // Make sure we preserve any originalId that might exist
      return [...prev, {
        ...service,
        id: maxId + 1,
        quantity: 1,
        // Preserve the originalId if it exists
        originalId: service.originalId || undefined
      }];
    });
  };

  const updateQuantity = (serviceId: number, serviceName: string, change: number) => {
    setServices(prev =>
      prev.map(service =>
        service.id === serviceId && service.name === serviceName
          ? { ...service, quantity: Math.max(0, service.quantity + change) }
          : service
      ).filter(service => service.quantity > 0)
    );
  };

  // Calculate subtotal (sum of service prices)
  const subtotal = services.reduce((sum, service) => sum + service.price * service.quantity, 0);
  // Calculate tax (10% of subtotal)
  const tax = subtotal * 0.1; // 10% tax
  // Calculate total (subtotal + tax)
  const total = subtotal + tax;

  const handleNext = () => {
    if (currentStep === 'services') {
      // Check if all services have staff assigned
      if (!areAllStaffAssigned()) {
        const unassignedServices = services.filter(service => !selectedStaff[service.id]);
        setValidationError(`Please assign staff for ${unassignedServices.length > 1 ? 'all services' : 'the service'}: ${unassignedServices.map(s => s.name).join(', ')}`);
        return;
      }

      // Debug log to verify services before moving to summary
      console.log('Services before moving to summary:', services);

      // Clear any previous validation errors
      setValidationError('');
      setCurrentStep('summary');
    } else if (currentStep === 'summary') {
      setCurrentStep('success');

      // Automatically complete the checkout after 2 seconds
      setTimeout(() => {
        handleComplete();
      }, 2000);
    }
  };

  const handleBack = () => {
    if (currentStep === 'summary') {
      setCurrentStep('services');
    }
  };

  const handleComplete = () => {
    // Pass the updated services, staff assignments, and payment method to the parent component
    console.log('Completing checkout with services:', services);
    console.log('Staff assignments:', selectedStaff);
    console.log('Payment method selected in checkout:', paymentMethod);

    // Make sure each service has the necessary information
    const processedServices = services.map(service => ({
      ...service,
      // Make sure we preserve the originalId if it exists
      originalId: service.originalId || undefined
    }));

    // Validate payment method before passing it to the parent
    if (!paymentMethod || (paymentMethod !== 'card' && paymentMethod !== 'cash' && paymentMethod !== 'upi')) {
      console.warn('Invalid payment method in checkout:', paymentMethod);
      console.log('Defaulting to card payment method');
      onComplete(processedServices, selectedStaff, 'card');
    } else {
      console.log('Passing valid payment method to parent:', paymentMethod);
      onComplete(processedServices, selectedStaff, paymentMethod);
    }

    onClose();
  };

  const resetCheckout = () => {
    setCurrentStep('services');
    setServices(initialServices);
    setSelectedStaff({});
    setValidationError('');
  };

  const handleCloseDialog = () => {
    resetCheckout();
    onClose();
  };

  const getStaffName = (staffId: number) => {
    if (!availableStaff || availableStaff.length === 0) {
      console.warn('No staff available when trying to get staff name');
      return 'Not assigned';
    }

    const staff = availableStaff.find(s => s.id === staffId);
    if (!staff) {
      console.warn(`Staff with ID ${staffId} not found in availableStaff:`, availableStaff);
      return 'Not assigned';
    }

    return staff.name;
  };

  const areAllStaffAssigned = () => {
    // Check if all services have staff assigned
    for (const service of services) {
      if (!selectedStaff[service.id]) {
        return false;
      }
    }
    return true;
  };

  // Debug logging
  console.log('Available staff:', availableStaff);
  console.log('Initial services:', initialServices);
  console.log('Available services:', availableServices);
  console.log('Current services:', services);

  const renderServicesStep = () => (
    <>
      <div className="mb-4">
        <h2 className="text-xl font-bold">Checkout</h2>
      </div>

      <div className="space-y-6 py-4">
        <div className="border-0 shadow-none">
          <div className="p-0">
            <div className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Current Services</h4>
                {services.map(service => (
                  <div key={service.id} className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{service.name}</p>
                      <p className="text-sm text-gray-500">₹{service.price}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="secondary"
                        size="icon"
                        onClick={() => updateQuantity(service.id, service.name, -1)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <span className="w-8 text-center">{service.quantity}</span>
                      <Button
                        variant="secondary"
                        size="icon"
                        onClick={() => updateQuantity(service.id, service.name, 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => updateQuantity(service.id, service.name, -service.quantity)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Additional Services</h4>
                <div className="grid grid-cols-2 gap-3">
                  {availableServices.map(service => (
                    <Button
                      key={service.id}
                      variant="secondary"
                      className="h-auto p-4 flex flex-col items-start shadow-sm hover:shadow transition-all"
                      onClick={() => addService(service)}
                    >
                      <span className="font-medium">{service.name}</span>
                      <span className="text-sm text-gray-500">₹{service.price}</span>
                    </Button>
                  ))}
                </div>
              </div>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Assign Staff</h4>
                {services.map(service => (
                  <div key={service.id} className="space-y-2">
                    <Label className="font-medium">
                      {service.name} <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={selectedStaff[service.id]?.toString()}
                      onValueChange={(value) => {
                        setSelectedStaff(prev => ({
                          ...prev,
                          [service.id]: Number(value)
                        }));
                        // Clear validation error when staff is assigned
                        if (validationError) setValidationError('');
                      }}
                    >
                      <SelectTrigger className={`bg-white ${!selectedStaff[service.id] && validationError ? 'border-red-500 ring-red-200' : ''}`}>
                        <SelectValue placeholder="Select staff member" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableStaff.length > 0 ? (
                          availableStaff.map(staff => (
                            <SelectItem key={staff.id} value={staff.id.toString()}>
                              {staff.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="0" disabled>No staff available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                ))}
                {validationError && (
                  <div className="text-red-500 text-sm mt-2 p-2 bg-red-50 border border-red-100 rounded">
                    {validationError}
                  </div>
                )}
              </div>

              <Separator className="my-6" />

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Subtotal</span>
                  <span className="font-medium">₹{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Tax (10%)</span>
                  <span className="font-medium">₹{tax.toFixed(2)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between text-lg font-medium">
                  <span>Total</span>
                  <span>₹{total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-2 mt-6">
        <Button variant="outline" onClick={handleCloseDialog}>Cancel</Button>
        <Button
          onClick={handleNext}
          size="lg"
          className="bg-indigo-600 hover:bg-indigo-700 text-white"
          disabled={
            services.length === 0 ||
            !areAllStaffAssigned()
          }
        >
          Review Order
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </>
  );

  const renderSummaryStep = () => {
    // Debug log to verify services in summary step
    console.log('Services in summary step:', services);

    return (
    <>
      <div className="mb-4">
        <h2 className="text-xl font-bold">Order Summary</h2>
      </div>

      <div className="space-y-6 py-4">
        <div className="border-0 shadow-none">
          <div className="p-0">
            <div className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Services</h4>
                {services.map(service => (
                  <div key={service.id} className="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{service.name}</p>
                      <div className="flex justify-between">
                        <p className="text-sm text-gray-500">
                          ₹{service.price} × {service.quantity}
                        </p>
                        <p className="text-sm font-medium">
                          ₹{(service.price * service.quantity).toFixed(2)}
                        </p>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        Staff: {selectedStaff[service.id] ? getStaffName(selectedStaff[service.id]) : 'Not assigned'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h4 className="font-semibold text-lg">Payment Method</h4>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    variant={paymentMethod === 'card' ? 'default' : 'outline'}
                    className="h-auto p-4 flex items-center justify-center"
                    onClick={() => setPaymentMethod('card')}
                  >
                    <CreditCard className="mr-2 h-5 w-5" />
                    <span className="font-medium">Credit Card</span>
                  </Button>
                  <Button
                    variant={paymentMethod === 'cash' ? 'default' : 'outline'}
                    className="h-auto p-4 flex items-center justify-center"
                    onClick={() => setPaymentMethod('cash')}
                  >
                    <Receipt className="mr-2 h-5 w-5" />
                    <span className="font-medium">Cash</span>
                  </Button>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2 bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Subtotal</span>
                  <span className="font-medium">₹{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Tax (10%)</span>
                  <span className="font-medium">₹{tax.toFixed(2)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span>₹{total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={handleBack}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleNext}
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          Complete Payment
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </>
    );
  };

  const renderSuccessStep = () => (
    <>
      <div className="flex flex-col items-center justify-center py-8 text-center space-y-4">
        <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-2">
          <CheckCircle className="h-10 w-10 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold">Payment Successful!</h2>
        <p className="text-gray-500 max-w-md">
          Your payment of <span className="font-medium">₹{total.toFixed(2)}</span> has been processed successfully.
        </p>

        <div className="bg-gray-50 p-4 rounded-lg w-full max-w-md mt-4">
          <div className="text-left space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-500">Payment method:</span>
              <span className="font-medium">{paymentMethod === 'card' ? 'Credit Card' : 'Cash'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Transaction ID:</span>
              <span className="font-medium">TXN{Math.floor(Math.random() * 1000000)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Date:</span>
              <span className="font-medium">{new Date().toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Auto-completing, no need for a button */}
    </>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={handleCloseDialog}
      />
      <div className="relative bg-white rounded-lg shadow-lg sm:max-w-[600px] w-full mx-4 p-6 max-h-[90vh] overflow-y-auto">
        {currentStep === 'services' && renderServicesStep()}
        {currentStep === 'summary' && renderSummaryStep()}
        {currentStep === 'success' && renderSuccessStep()}
      </div>
    </div>
  );
}