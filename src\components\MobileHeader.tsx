import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Menu, X, LogOut, Settings, ChevronLeft, Store } from 'lucide-react';
import { useAuth } from '../contexts/SimpleAuthContext';
import { supabase } from '../contexts/SimpleAuthContext';

export function MobileHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [salonName, setSalonName] = useState('Salon Orbit');
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuth();

  // Fetch salon name
  useEffect(() => {
    const fetchSalonData = async () => {
      if (user?.id) {
        try {
          const { data: salonData, error: salonError } = await supabase
            .from('salons')
            .select('name')
            .eq('owner_user_id', user.id)
            .single();

          if (!salonError && salonData?.name) {
            setSalonName(salonData.name);
          }
        } catch (error) {
          console.error('Error fetching salon data:', error);
        }
      }
    };

    fetchSalonData();
  }, [user]);

  const handleLogout = async () => {
    await logout();
    navigate('/auth');
  };

  // Get page title based on current path
  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/dashboard') return salonName;
    if (path === '/appointments') return 'Appointments';
    if (path === '/clients') return 'Clients';
    if (path === '/services') return 'Services';
    if (path === '/staff') return 'Staff';
    if (path === '/settings') return 'Settings';
    return 'Salon Orbit';
  };

  // Check if we're on a page other than dashboard to show back button
  const showBackButton = location.pathname !== '/dashboard';

  // Determine if we're on the dashboard to use a different header style
  const isDashboard = location.pathname === '/dashboard';

  return (
    <>
      <header className={`sticky top-0 z-40 shadow-sm ${isDashboard
        ? 'bg-white border-b border-gray-100'
        : 'bg-gradient-to-r from-indigo-500/90 to-purple-500/90'}`}>
        <div className="h-16 px-4 flex items-center justify-between">
          <div className="flex items-center">
            {showBackButton ? (
              <button
                onClick={() => navigate(-1)}
                className={`mr-3 p-1 rounded-full ${isDashboard
                  ? 'hover:bg-gray-100 text-gray-600'
                  : 'hover:bg-white/10 text-white'}`}
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
            ) : (
              <Store className={`h-5 w-5 mr-2 ${isDashboard ? 'text-indigo-600' : 'text-white'}`} />
            )}
            <h1 className={`text-lg font-semibold ${isDashboard
              ? 'bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600'
              : 'text-white'}`}>
              {getPageTitle()}
            </h1>
            {isDashboard && (
              <span className="ml-1.5 text-xs font-medium text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                Lite
              </span>
            )}
          </div>

          <button
            onClick={() => setIsMenuOpen(true)}
            className={`p-2 rounded-full ${isDashboard
              ? 'hover:bg-gray-100 text-gray-600'
              : 'hover:bg-white/10 text-white'}`}
          >
            <Menu className="h-5 w-5" />
          </button>
        </div>
      </header>

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 bg-black/50" onClick={() => setIsMenuOpen(false)}>
          <div
            className="absolute right-0 top-0 bottom-0 w-72 bg-white shadow-xl flex flex-col"
            onClick={e => e.stopPropagation()}
          >
            {/* Header with gradient background */}
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-5 text-white">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Menu</h2>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-1 rounded-full hover:bg-white/10"
                >
                  <X className="h-5 w-5 text-white" />
                </button>
              </div>

              <div className="flex items-center">
                <div className="h-12 w-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white font-medium text-lg mr-3 shadow-sm">
                  {user?.email ? user.email[0].toUpperCase() : 'U'}
                </div>
                <div>
                  <h3 className="font-medium">{user?.email}</h3>
                  <p className="text-xs text-white/80">{salonName}</p>
                </div>
              </div>
            </div>

            {/* Menu content */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="mb-6">
                <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => {
                      navigate('/appointments');
                      window.dispatchEvent(new CustomEvent('open-appointment-dialog'));
                      setIsMenuOpen(false);
                    }}
                    className="flex flex-col items-center justify-center p-3 bg-indigo-50 rounded-lg text-indigo-600 hover:bg-indigo-100 transition-colors"
                  >
                    <Calendar className="h-6 w-6 mb-1" />
                    <span className="text-xs font-medium">New Appointment</span>
                  </button>

                  <button
                    onClick={() => {
                      navigate('/clients');
                      window.dispatchEvent(new CustomEvent('open-client-dialog'));
                      setIsMenuOpen(false);
                    }}
                    className="flex flex-col items-center justify-center p-3 bg-purple-50 rounded-lg text-purple-600 hover:bg-purple-100 transition-colors"
                  >
                    <Users className="h-6 w-6 mb-1" />
                    <span className="text-xs font-medium">New Client</span>
                  </button>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">
                  Settings
                </h3>
                <div className="space-y-1">
                  <button
                    onClick={() => {
                      navigate('/settings');
                      setIsMenuOpen(false);
                    }}
                    className="w-full flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                  >
                    <Settings className="h-4 w-4 mr-3 text-gray-500" />
                    Salon Settings
                  </button>

                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center px-3 py-2.5 text-sm text-red-600 hover:bg-red-50 rounded-md"
                  >
                    <LogOut className="h-4 w-4 mr-3 text-red-500" />
                    Logout
                  </button>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-100 text-center">
              <div className="font-medium text-indigo-600 text-sm mb-1">Salon Orbit Lite</div>
              <div className="text-xs text-gray-500">Version 1.0.0</div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
