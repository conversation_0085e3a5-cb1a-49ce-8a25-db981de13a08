// Simple test to verify the WhatsApp bot fix
require('dotenv').config();

const express = require('express');
const sessionManager = require('./wbot/session-manager');
const db = require('./wbot/db');

const app = express();
app.use(express.json());

// Enable CORS for local testing
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Test status endpoint with the fix
app.get("/test-status/:salonId", async (req, res) => {
    try {
        const { salonId } = req.params;
        
        console.log(`🔍 Testing status for salon ${salonId}`);
        
        // Check if salon exists in database
        const salon = await db.getSalon(salonId);
        if (!salon) {
            return res.json({
                success: false,
                message: `Salon with ID ${salonId} not found`,
            });
        }
        
        console.log(`📊 Salon found: ${salon.name}`);
        console.log(`📊 WhatsApp enabled: ${salon.whatsapp_enabled}`);
        console.log(`📊 Auth state exists: ${salon.auth_state ? 'YES' : 'NO'}`);
        
        // Test old authentication check (memory only)
        const memoryAuthenticated = sessionManager.isAuthenticated(salonId);
        console.log(`📊 Memory authenticated: ${memoryAuthenticated}`);
        
        // Test new authentication check (memory + database)
        const dbAuthenticated = await sessionManager.isAuthenticatedWithDB(salonId);
        console.log(`📊 Database authenticated: ${dbAuthenticated}`);
        
        res.json({
            success: true,
            salonId,
            salon_name: salon.name,
            whatsapp_enabled: salon.whatsapp_enabled,
            has_auth_state: !!salon.auth_state,
            memory_authenticated: memoryAuthenticated,
            database_authenticated: dbAuthenticated,
            fix_working: memoryAuthenticated !== dbAuthenticated ? 'YES - Fix detected mismatch!' : 'No mismatch detected'
        });
        
    } catch (error) {
        console.error("Error in test:", error);
        res.status(500).json({
            success: false,
            message: "Test failed",
            error: error.message,
        });
    }
});

// Health check
app.get("/health", (req, res) => {
    res.json({
        status: "ok",
        message: "Test bot is running",
        timestamp: new Date().toISOString(),
    });
});

// Start server
const PORT = 3001; // Use different port to avoid conflicts
app.listen(PORT, () => {
    console.log(`🧪 Test bot running on port ${PORT}`);
    console.log(`Test the fix at: http://localhost:${PORT}/test-status/d0a56955-eba7-4679-b0de-c9946505c1af`);
});
