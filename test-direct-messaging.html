<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Direct Messaging Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #25D366;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #25D366;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #128C7E;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>🚀 WhatsApp Direct Messaging Test</h1>
    
    <div class="container">
        <h2>Test Direct Message Sending</h2>
        <p>This tests the new direct sending flow that prioritizes immediate delivery over queuing.</p>
        
        <div class="form-group">
            <label for="phoneNumber">Phone Number (10 digits):</label>
            <input type="text" id="phoneNumber" placeholder="9876543210" maxlength="10">
        </div>
        
        <div class="form-group">
            <label for="messageText">Message:</label>
            <textarea id="messageText" placeholder="Test message for direct sending...">🧪 Test Message - Direct Sending Priority

This is a test of the new WhatsApp flow that:
✅ Tries direct sending first
❌ Only queues if direct sending fails

Timestamp: ${new Date().toLocaleString()}</textarea>
        </div>
        
        <button onclick="sendDirectMessage()">📱 Send Direct Message</button>
        <button onclick="simulateAppointmentNotification()">📅 Simulate Appointment Notification</button>
        
        <div id="messageResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>Check Queue Status</h2>
        <p>Check if any messages are currently queued (should be minimal with direct sending).</p>
        
        <button onclick="checkQueueStatus()">📊 Check Queue Status</button>
        <button onclick="processQueue()">🔄 Process Queue (if any)</button>
        
        <div id="queueResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const SUPABASE_URL = "https://mixjfinrxzpplzqidlas.supabase.co";
        const SALON_ID = "d0a56955-eba7-4679-b0de-c9946505c1af";
        const BOT_URL = "https://wb-userbot-production.up.railway.app";

        async function sendDirectMessage() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const message = document.getElementById('messageText').value;
            const resultDiv = document.getElementById('messageResult');

            if (!phoneNumber || !message) {
                showResult('messageResult', 'Please enter both phone number and message', 'error');
                return;
            }

            showResult('messageResult', 'Sending message directly...', 'info');

            try {
                // Format phone number
                let formattedPhone = phoneNumber.replace(/\D/g, '');
                if (!formattedPhone.startsWith('91') && formattedPhone.length === 10) {
                    formattedPhone = '91' + formattedPhone;
                }

                const response = await fetch(`${BOT_URL}/session/${SALON_ID}/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phoneNumber: formattedPhone,
                        message: message.replace('${new Date().toLocaleString()}', new Date().toLocaleString()),
                    }),
                });
                const result = await response.json();

                if (response.ok && result.success) {
                    showResult('messageResult', `✅ Message sent directly!\n\nResponse: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult('messageResult', `❌ Direct sending failed:\n\n${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('messageResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function simulateAppointmentNotification() {
            const resultDiv = document.getElementById('messageResult');
            showResult('messageResult', 'Simulating appointment notification (direct sending priority)...', 'info');

            try {
                // This would normally be triggered by a database trigger
                // For testing, we'll call the edge function directly
                const response = await fetch(`${SUPABASE_URL}/functions/v1/appointment-notifications`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: 'INSERT',
                        table: 'appointments',
                        record: {
                            appointment_id: 'test-' + Date.now(),
                            salon_id: SALON_ID,
                            client_id: 'test-client',
                            appointment_date: new Date(Date.now() + 24*60*60*1000).toISOString(),
                            status: 'scheduled',
                            created_at: new Date().toISOString(),
                            updated_at: new Date().toISOString()
                        }
                    }),
                });
                const result = await response.json();

                if (response.ok) {
                    showResult('messageResult', `✅ Appointment notification processed!\n\nDelivery Method: ${result.delivery_method || 'unknown'}\n\nResponse: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult('messageResult', `❌ Notification failed:\n\n${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('messageResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function checkQueueStatus() {
            const resultDiv = document.getElementById('queueResult');
            showResult('queueResult', 'Checking queue status...', 'info');

            try {
                // Note: This would require a custom endpoint to check queue status
                // For now, we'll just show a message
                showResult('queueResult', '📊 Queue Status Check\n\nWith direct sending priority:\n- Most messages should be sent immediately\n- Queue should only contain failed messages\n- Check Supabase dashboard for actual queue table data', 'info');
            } catch (error) {
                showResult('queueResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function processQueue() {
            const resultDiv = document.getElementById('queueResult');
            showResult('queueResult', 'Processing queue...', 'info');

            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/manual-queue-processor`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                const result = await response.json();

                if (response.ok) {
                    showResult('queueResult', `✅ Queue processed!\n\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult('queueResult', `❌ Queue processing failed:\n\n${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('queueResult', `❌ Error: ${error.message}`, 'error');
            }
        }

        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
    </script>
</body>
</html>
