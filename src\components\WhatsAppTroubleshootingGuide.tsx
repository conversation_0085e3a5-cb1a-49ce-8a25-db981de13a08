import React from 'react';
import { AlertCircle, CheckCircle, XCircle, RefreshCw, Smartphone, QrCode, Server } from 'lucide-react';

interface WhatsAppTroubleshootingGuideProps {
  onReconnect?: () => void;
  apiBaseUrl?: string;
}

export function WhatsAppTroubleshootingGuide({ onReconnect, apiBaseUrl }: WhatsAppTroubleshootingGuideProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-4 bg-indigo-50 border-b border-indigo-100">
        <h3 className="text-lg font-medium text-indigo-900 flex items-center">
          <AlertCircle className="h-5 w-5 mr-2 text-indigo-600" />
          WhatsApp Connection Troubleshooting
        </h3>
      </div>

      <div className="p-4 space-y-4">
        <p className="text-gray-700">
          If you're experiencing issues with your WhatsApp connection, follow these steps to resolve them:
        </p>

        <div className="space-y-4">
          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center mb-2">
              <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
              Common Issues
            </h4>
            <ul className="list-disc pl-6 text-sm text-gray-700 space-y-1">
              <li><strong>Multiple WhatsApp Web sessions active</strong> (most common)</li>
              <li>WhatsApp session expired or logged out</li>
              <li>Phone battery died or WhatsApp was closed</li>
              <li>Internet connection issues on your phone</li>
              <li>WhatsApp was logged in on another device</li>
            </ul>
          </div>

          <div className="bg-red-50 p-3 rounded-lg border border-red-200">
            <h4 className="font-medium text-red-900 flex items-center mb-2">
              <XCircle className="h-4 w-4 mr-2 text-red-600" />
              Fix WhatsApp Web Conflicts (Most Important)
            </h4>
            <ol className="list-decimal pl-6 text-sm text-red-800 space-y-2">
              <li>
                <strong>Open WhatsApp on your phone</strong>
              </li>
              <li>
                <strong>Go to Settings → Linked Devices</strong>
              </li>
              <li>
                <strong>Log out from ALL other WhatsApp Web sessions</strong>
              </li>
              <li>
                <strong>Click "Disconnect WhatsApp" in the app settings</strong>
              </li>
              <li>
                <strong>Click "Connect WhatsApp" to get a fresh QR code</strong>
              </li>
              <li>
                <strong>Scan the new QR code</strong>
              </li>
            </ol>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center mb-2">
              <RefreshCw className="h-4 w-4 mr-2 text-blue-600" />
              General Reconnection Steps
            </h4>
            <ol className="list-decimal pl-6 text-sm text-gray-700 space-y-2">
              <li>
                <strong>Check your phone:</strong> Make sure WhatsApp is running and has an internet connection
              </li>
              <li>
                <strong>Reconnect:</strong> Click the "Connect WhatsApp" button in settings
              </li>
              <li>
                <strong>Scan QR code:</strong> Open WhatsApp on your phone, go to Menu &gt; Linked Devices &gt; Link a Device
              </li>
              <li>
                <strong>Wait for confirmation:</strong> After scanning, wait for the connection confirmation
              </li>
            </ol>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center mb-2">
              <XCircle className="h-4 w-4 mr-2 text-red-600" />
              Common Errors
            </h4>
            <div className="space-y-3 text-sm">
              <div>
                <p className="font-medium text-gray-800">Authentication Error:</p>
                <p className="text-gray-700 ml-4">
                  "WhatsApp connection not authenticated. Please scan the QR code first."
                </p>
                <p className="text-gray-600 ml-4 mt-1">
                  Solution: Reconnect by scanning a new QR code
                </p>
              </div>
              <div>
                <p className="font-medium text-gray-800">Connection Error:</p>
                <p className="text-gray-700 ml-4">
                  "Failed to initialize WhatsApp connection"
                </p>
                <p className="text-gray-600 ml-4 mt-1">
                  Solution: Check your internet connection and try again
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center mb-2">
              <Smartphone className="h-4 w-4 mr-2 text-indigo-600" />
              Phone Requirements
            </h4>
            <ul className="list-disc pl-6 text-sm text-gray-700 space-y-1">
              <li>WhatsApp must be installed and active on your phone</li>
              <li>Your phone must have an active internet connection</li>
              <li>Battery optimization should be disabled for WhatsApp</li>
              <li>Keep your phone powered on for WhatsApp to work</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center mb-2">
              <QrCode className="h-4 w-4 mr-2 text-purple-600" />
              QR Code Tips
            </h4>
            <ul className="list-disc pl-6 text-sm text-gray-700 space-y-1">
              <li>Make sure your phone camera is clean and can focus properly</li>
              <li>Ensure good lighting when scanning the QR code</li>
              <li>Hold your phone steady while scanning</li>
              <li>If scanning fails, try refreshing the QR code</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
            <h4 className="font-medium text-gray-900 flex items-center mb-2">
              <Server className="h-4 w-4 mr-2 text-orange-600" />
              API Server Status
            </h4>
            <div className="text-sm text-gray-700">
              <p className="mb-2">
                The WhatsApp integration requires a connection to the WhatsApp API server at:
              </p>
              <div className="bg-gray-100 p-2 rounded font-mono text-xs mb-3 break-all">
                {apiBaseUrl || 'https://wb-userbot-production.up.railway.app'}
              </div>
              <p className="mb-2">If you're experiencing connection issues:</p>
              <ul className="list-disc pl-6 space-y-1">
                <li>Check that the API server is running and accessible</li>
                <li>Ensure your internet connection is working properly</li>
                <li>Check for any firewall or network restrictions</li>
                <li>Try again later if the server might be temporarily down</li>
              </ul>
            </div>
          </div>
        </div>

        {onReconnect && (
          <div className="flex justify-center mt-4">
            <button
              onClick={onReconnect}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reconnect WhatsApp
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
