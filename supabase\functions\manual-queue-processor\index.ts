import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("🚀 Manual queue processor triggered");

    // Call the existing queue processor
    const response = await fetch(
      `${Deno.env.get("SUPABASE_URL")}/functions/v1/whatsapp-queue-processor`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")}`,
        },
        body: JSON.stringify({}),
      },
    );

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Queue processor failed: ${result.error || response.statusText}`);
    }

    console.log("✅ Manual queue processing completed:", result);

    return new Response(
      JSON.stringify({
        success: true,
        message: "Manual queue processing completed",
        result: result,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      },
    );
  } catch (error) {
    console.error("Error in manual queue processor:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      },
    );
  }
});
