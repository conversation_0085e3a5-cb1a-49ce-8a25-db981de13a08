import { supabase } from "../contexts/SimpleAuthContext";
import { AppointmentStatus } from "../types/appointment";
import { getCurrentUserSalonId } from "./salonService";

export interface Appointment {
  appointment_id: string;
  salon_id: string;
  client_id: string;
  appointment_date: string; // TIMESTAMP in ISO format
  duration: number;
  status: AppointmentStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
  // Joined fields
  client_name?: string;
  service_name?: string;
  staff_name?: string;
  price?: number; // From appointment_services
  paymentMethod?: string; // From transactions
}

export const getAppointments = async (
  salonId?: string,
): Promise<Appointment[]> => {
  try {
    // If salonId is undefined, try to get the current user's salon ID
    if (!salonId) {
      salonId = await getCurrentUserSalonId();
      console.log("Retrieved salon ID from current user:", salonId);

      // If we still don't have a valid salon ID, return an empty array
      if (!salonId) {
        console.error(
          "No salon ID provided and could not retrieve from current user",
        );
        return [];
      }
    }

    // First, get all appointments for the salon
    const { data: appointmentsData, error: appointmentsError } = await supabase
      .from("appointments")
      .select(`
        *,
        clients(first_name, last_name)
      `)
      .eq("salon_id", salonId)
      .order("appointment_date", { ascending: false }); // Changed to descending order to show latest appointments first

    if (appointmentsError) {
      console.error("Error fetching appointments:", appointmentsError);
      throw appointmentsError;
    }

    if (!appointmentsData || appointmentsData.length === 0) {
      return [];
    }

    // Get all appointment services for these appointments
    const appointmentIds = appointmentsData.map((a) => a.appointment_id);
    const { data: servicesData, error: servicesError } = await supabase
      .from("appointment_services")
      .select(`
        *,
        services(name, price, duration)
      `)
      .in("appointment_id", appointmentIds);

    if (servicesError) {
      console.error("Error fetching appointment services:", servicesError);
      // Continue with just the appointment data
    }

    // Get all staff members for these appointments
    const staffIds = appointmentsData
      .map((a) => a.staff_id)
      .filter((id) => id !== null && id !== undefined);

    let staffMap = new Map();
    if (staffIds.length > 0) {
      const { data: staffData, error: staffError } = await supabase
        .from("users")
        .select("user_id, first_name, last_name")
        .in("user_id", staffIds);

      if (!staffError && staffData) {
        staffMap = new Map(
          staffData.map((staff) => [
            staff.user_id,
            `${staff.first_name} ${staff.last_name}`,
          ]),
        );
      } else {
        console.error("Error fetching staff details:", staffError);
      }
    }

    // Transform the data to include joined fields
    const appointments = appointmentsData.map((appointment) => {
      // Find all services for this appointment
      const appointmentServices = servicesData?.filter((s) =>
        s.appointment_id === appointment.appointment_id
      ) || [];

      // Get service details
      const services = appointmentServices.map((as) => ({
        name: as.services?.name || "",
        price: as.services?.price || 0,
        duration: as.services?.duration || 0,
      }));

      // Calculate total price from all services
      const totalPrice = services.reduce((sum, s) =>
        sum + s.price, 0);

      // Get staff name from the map
      const staffName = appointment.staff_id
        ? staffMap.get(appointment.staff_id) || ""
        : "";

      return {
        ...appointment,
        client_name: `${appointment.clients?.first_name || ""} ${
          appointment.clients?.last_name || ""
        }`.trim(),
        services: services,
        service_name: services.map((s) => s.name).join(", "),
        price: totalPrice,
        staff_name: staffName,
      };
    });

    return appointments;
  } catch (error) {
    console.error("Error in getAppointments:", error);
    return [];
  }
};

export const addAppointment = async (appointment: {
  salon_id: string;
  client_id: string;
  service_ids: string[];
  staff_id: string;
  appointment_date: string;
  status?: AppointmentStatus;
  notes?: string;
}): Promise<Appointment | null> => {
  try {
    // Calculate total duration and get service details
    const { data: servicesData, error: servicesError } = await supabase
      .from("services")
      .select("service_id, price, duration, name")
      .in("service_id", appointment.service_ids);

    if (servicesError) {
      console.error("Error fetching service details:", servicesError);
      throw servicesError;
    }

    const totalDuration = servicesData.reduce(
      (sum, service) => sum + service.duration,
      0,
    );

    // Create the appointment record
    const { data: appointmentData, error: appointmentError } = await supabase
      .from("appointments")
      .insert([{
        salon_id: appointment.salon_id,
        client_id: appointment.client_id,
        staff_id: appointment.staff_id,
        appointment_date: appointment.appointment_date,
        status: appointment.status || "scheduled",
        duration: totalDuration,
        notes: appointment.notes,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single();

    if (appointmentError) {
      console.error("Error adding appointment:", appointmentError);
      throw appointmentError;
    }

    // Create appointment_services records for each service
    const appointmentServices = servicesData.map((service) => ({
      appointment_id: appointmentData.appointment_id,
      service_id: service.service_id,
      price: service.price,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }));

    const { error: serviceAssignError } = await supabase
      .from("appointment_services")
      .insert(appointmentServices);

    if (serviceAssignError) {
      console.error("Error adding appointment services:", serviceAssignError);
      throw serviceAssignError;
    }

    // Calculate total price from all services
    const totalPrice = servicesData.reduce(
      (sum, service) => sum + service.price,
      0,
    );

    // Get staff name
    let staffName = "";
    if (appointmentData.staff_id) {
      const { data: staffData, error: staffError } = await supabase
        .from("users")
        .select("first_name, last_name")
        .eq("user_id", appointmentData.staff_id)
        .single();

      if (!staffError && staffData) {
        staffName = `${staffData.first_name} ${staffData.last_name}`;
      }
    }

    // Return the appointment with additional details
    return {
      ...appointmentData,
      services: servicesData.map((service) => ({
        name: service.name,
        price: service.price,
      })),
      service_name: servicesData.map((service) => service.name).join(", "),
      price: totalPrice,
      staff_name: staffName,
    };
  } catch (error) {
    console.error("Error in addAppointment:", error);
    return null;
  }
};

export const updateAppointmentStatus = async (
  appointmentId: string,
  status: AppointmentStatus,
  paymentMethod: string = "card", // Default to 'card' if not provided
  staffId?: string, // Optional staff ID for pending appointments
): Promise<Appointment | null> => {
  console.log(
    "updateAppointmentStatus called with payment method:",
    paymentMethod,
  );
  try {
    // Create update object
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    };

    // If staffId is provided, update the staff_id field
    if (staffId) {
      updateData.staff_id = staffId;
    }

    // Update the appointment status
    const { data, error } = await supabase
      .from("appointments")
      .update(updateData)
      .eq("appointment_id", appointmentId)
      .select()
      .single();

    if (error) {
      console.error("Error updating appointment status:", error);
      throw error;
    }

    // If the status is completed and payment method is provided, create a transaction record
    if (status === "completed" && paymentMethod) {
      try {
        console.log(
          "Creating transaction record with payment method:",
          paymentMethod,
        );

        // Get the appointment details to calculate total amount
        const appointment = await getAppointment(appointmentId);

        if (appointment) {
          // Get the current user ID
          const { data: userData } = await supabase.auth.getUser();
          const userId = userData?.user?.id;

          if (userId) {
            // Create a transaction record
            const transactionRecord = {
              salon_id: appointment.salon_id,
              appointment_id: appointmentId,
              client_id: appointment.client_id,
              user_id: userId,
              total_amount: appointment.price || 0,
              payment_method: paymentMethod,
              status: "completed",
              transaction_date: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            };

            console.log("Transaction record to insert:", transactionRecord);

            const { data: transactionData, error: transactionError } =
              await supabase
                .from("transactions")
                .insert([transactionRecord])
                .select()
                .single();

            if (transactionError) {
              console.error(
                "Error creating transaction record:",
                transactionError,
              );
              // Continue even if transaction creation fails
            } else {
              console.log(
                "Transaction record created successfully:",
                transactionData,
              );
            }
          }
        }
      } catch (transactionError) {
        console.error("Error in transaction creation:", transactionError);
        // Continue even if transaction creation fails
      }
    }

    // After updating, fetch the complete appointment data with services
    return getAppointment(appointmentId);
  } catch (error) {
    console.error("Error in updateAppointmentStatus:", error);
    return null;
  }
};

export const updateAppointmentDate = async (
  appointmentId: string,
  newDate: string,
  staffId?: string,
): Promise<boolean> => {
  try {
    const updateData: any = {
      appointment_date: newDate,
      updated_at: new Date().toISOString(),
    };

    // If staffId is provided, update the staff assignment
    if (staffId) {
      updateData.staff_id = staffId;
    }

    const { error } = await supabase
      .from("appointments")
      .update(updateData)
      .eq("appointment_id", appointmentId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error("Error updating appointment date:", error);
    return false;
  }
};

export const updateAppointmentServicesAndStatus = async (
  appointmentId: string,
  services: Array<{
    serviceId?: string;
    name: string;
    price: number;
    staffId?: number;
    originalId?: string;
  }>,
  status: AppointmentStatus,
  paymentMethod: string = "card",
): Promise<Appointment | null> => {
  try {
    // First update the services
    const updatedAppointment = await updateAppointmentServices(
      appointmentId,
      services,
    );

    if (!updatedAppointment) {
      throw new Error("Failed to update appointment services");
    }

    // Then update the status in the same transaction-like operation
    // We'll update the appointment record directly to avoid triggering multiple notifications
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("appointments")
      .update(updateData)
      .eq("appointment_id", appointmentId)
      .select()
      .single();

    if (error) {
      console.error("Error updating appointment status:", error);
      throw error;
    }

    // Handle transaction creation for completed appointments
    if (status === "completed" && paymentMethod) {
      try {
        console.log(
          "Creating transaction record with payment method:",
          paymentMethod,
        );

        // Get the appointment details to calculate total amount
        const appointment = await getAppointment(appointmentId);

        if (appointment) {
          // Get the current user ID
          const { data: userData } = await supabase.auth.getUser();
          const userId = userData?.user?.id;

          // Calculate total amount from services
          const totalAmount = appointment.services.reduce(
            (sum, service) => sum + service.price,
            0,
          );

          // Create transaction record
          const { data: transactionData, error: transactionError } =
            await supabase
              .from("transactions")
              .insert([{
                appointment_id: appointmentId,
                salon_id: appointment.salon_id,
                client_id: appointment.client_id,
                staff_id: appointment.staff_id,
                amount: totalAmount,
                payment_method: paymentMethod,
                transaction_date: new Date().toISOString(),
                created_by: userId,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              }])
              .select()
              .single();

          if (transactionError) {
            console.error(
              "Error creating transaction record:",
              transactionError,
            );
            // Continue even if transaction creation fails
          } else {
            console.log(
              "Transaction record created successfully:",
              transactionData,
            );
          }
        }
      } catch (transactionError) {
        console.error("Error in transaction creation:", transactionError);
        // Continue even if transaction creation fails
      }
    }

    // Return the updated appointment with full details
    return getAppointment(appointmentId);
  } catch (error) {
    console.error("Error in updateAppointmentServicesAndStatus:", error);
    return null;
  }
};

export const updateAppointmentDateAndStatus = async (
  appointmentId: string,
  newDate: string,
  status: AppointmentStatus,
  staffId?: string,
  paymentMethod: string = "card",
): Promise<Appointment | null> => {
  try {
    // Combine all updates into a single database operation
    const updateData: any = {
      appointment_date: newDate,
      status,
      updated_at: new Date().toISOString(),
    };

    // If staffId is provided, update the staff assignment
    if (staffId) {
      updateData.staff_id = staffId;
    }

    // Update the appointment in a single operation
    const { data, error } = await supabase
      .from("appointments")
      .update(updateData)
      .eq("appointment_id", appointmentId)
      .select()
      .single();

    if (error) {
      console.error("Error updating appointment date and status:", error);
      throw error;
    }

    // Handle transaction creation for completed appointments
    if (status === "completed" && paymentMethod) {
      try {
        console.log(
          "Creating transaction record with payment method:",
          paymentMethod,
        );

        // Get the appointment details to calculate total amount
        const appointment = await getAppointment(appointmentId);

        if (appointment) {
          // Get the current user ID
          const { data: userData } = await supabase.auth.getUser();
          const userId = userData?.user?.id;

          // Calculate total amount from services
          const totalAmount = appointment.services.reduce(
            (sum, service) => sum + service.price,
            0,
          );

          // Create transaction record
          const { data: transactionData, error: transactionError } =
            await supabase
              .from("transactions")
              .insert([{
                appointment_id: appointmentId,
                salon_id: appointment.salon_id,
                client_id: appointment.client_id,
                staff_id: appointment.staff_id,
                amount: totalAmount,
                payment_method: paymentMethod,
                transaction_date: new Date().toISOString(),
                created_by: userId,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              }])
              .select()
              .single();

          if (transactionError) {
            console.error(
              "Error creating transaction record:",
              transactionError,
            );
            // Continue even if transaction creation fails
          } else {
            console.log(
              "Transaction record created successfully:",
              transactionData,
            );
          }
        }
      } catch (transactionError) {
        console.error("Error in transaction creation:", transactionError);
        // Continue even if transaction creation fails
      }
    }

    // Return the updated appointment with full details
    return getAppointment(appointmentId);
  } catch (error) {
    console.error("Error in updateAppointmentDateAndStatus:", error);
    return null;
  }
};

export const deleteAppointment = async (
  appointmentId: string,
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("appointments")
      .delete()
      .eq("appointment_id", appointmentId);

    if (error) {
      console.error("Error deleting appointment:", error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error("Error in deleteAppointment:", error);
    return false;
  }
};

// Generate time slots from 8 AM to 8 PM in 15-minute intervals
export const getCommonTimeSlots = (): string[] => {
  const slots = [];
  for (let hour = 8; hour < 20; hour++) {
    for (let minute = 0; minute < 60; minute += 15) {
      const formattedHour = hour.toString().padStart(2, "0");
      const formattedMinute = minute.toString().padStart(2, "0");
      slots.push(`${formattedHour}:${formattedMinute}`);
    }
  }
  return slots;
};

// Function to update appointment services
export const updateAppointmentServices = async (
  appointmentId: string,
  services: Array<
    {
      serviceId?: string;
      name: string;
      price: number;
      staffId?: number;
      originalId?: string;
    }
  >,
): Promise<Appointment | null> => {
  try {
    // First, get existing appointment services
    const { data: existingServices, error: existingServicesError } =
      await supabase
        .from("appointment_services")
        .select("*")
        .eq("appointment_id", appointmentId);

    if (existingServicesError) {
      console.error(
        "Error fetching existing appointment services:",
        existingServicesError,
      );
      throw existingServicesError;
    }

    // Delete all existing appointment services
    const { error: deleteError } = await supabase
      .from("appointment_services")
      .delete()
      .eq("appointment_id", appointmentId);

    if (deleteError) {
      console.error(
        "Error deleting existing appointment services:",
        deleteError,
      );
      throw deleteError;
    }

    // For services with serviceId, we can directly add them
    // For services without serviceId (new services added during checkout), we need to find or create them
    const newAppointmentServices = [];

    for (const service of services) {
      if (service.serviceId) {
        // This is an existing service, just add it back
        newAppointmentServices.push({
          appointment_id: appointmentId,
          service_id: service.serviceId,
          price: service.price,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      } else {
        // This is a new service added during checkout
        // First, check if a service with this name already exists
        // Use ilike for case-insensitive matching and proper URL encoding
        const { data: existingServices, error: existingServiceError } =
          await supabase
            .from("services")
            .select("service_id")
            .ilike("name", service.name);

        if (existingServiceError) {
          console.error(
            "Error checking for existing service:",
            existingServiceError,
          );
          // Continue with the next service
          continue;
        }

        let serviceId;
        if (existingServices && existingServices.length > 0) {
          // Use the first matching existing service
          console.log("Found existing service:", existingServices[0]);
          serviceId = existingServices[0].service_id;
        } else {
          // Create a new service
          const { data: newService, error: newServiceError } = await supabase
            .from("services")
            .insert([{
              salon_id: (await getCurrentUserSalonId()) || "",
              name: service.name,
              price: service.price,
              duration: 30, // Default duration
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }])
            .select()
            .single();

          if (newServiceError) {
            console.error("Error creating new service:", newServiceError);
            // Continue with the next service
            continue;
          }

          serviceId = newService.service_id;
        }

        // Add the service to the appointment
        newAppointmentServices.push({
          appointment_id: appointmentId,
          service_id: serviceId,
          price: service.price,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }
    }

    // Insert all new appointment services
    if (newAppointmentServices.length > 0) {
      const { error: insertError } = await supabase
        .from("appointment_services")
        .insert(newAppointmentServices);

      if (insertError) {
        console.error("Error inserting new appointment services:", insertError);
        throw insertError;
      }
    }

    // Calculate the total price (sum of service prices)
    // This is the subtotal before tax
    const subtotal = services.reduce((sum, service) => sum + service.price, 0);

    // Update the appointment's updated_at timestamp
    // Note: We don't store the price in the appointments table
    const { error: updateError } = await supabase
      .from("appointments")
      .update({
        updated_at: new Date().toISOString(),
      })
      .eq("appointment_id", appointmentId);

    if (updateError) {
      console.error("Error updating appointment:", updateError);
      throw updateError;
    }

    // Return the updated appointment
    return getAppointment(appointmentId);
  } catch (error) {
    console.error("Error in updateAppointmentServices:", error);
    return null;
  }
};

export const getAppointment = async (
  appointmentId: string,
): Promise<Appointment | null> => {
  try {
    // First, fetch the basic appointment data with client details
    const { data: appointmentData, error: appointmentError } = await supabase
      .from("appointments")
      .select(`
        *,
        clients (
          first_name,
          last_name
        )
      `)
      .eq("appointment_id", appointmentId)
      .single();

    if (appointmentError) {
      throw appointmentError;
    }

    // Fetch staff details separately to avoid relationship issues
    let staffName = "";
    if (appointmentData.staff_id) {
      const { data: staffData, error: staffError } = await supabase
        .from("users")
        .select("first_name, last_name")
        .eq("user_id", appointmentData.staff_id)
        .single();

      if (!staffError && staffData) {
        staffName = `${staffData.first_name} ${staffData.last_name}`;
      }
    }

    // Fetch services for this appointment
    const { data: servicesData, error: servicesError } = await supabase
      .from("appointment_services")
      .select(`
        *,
        services (
          name,
          price,
          duration
        )
      `)
      .eq("appointment_id", appointmentId);

    if (servicesError) {
      throw servicesError;
    }

    // Transform services data
    const services = servicesData.map((as) => ({
      id: as.service_id, // Include the service ID
      name: as.services?.name || "",
      price: as.services?.price || 0,
      duration: as.services?.duration || 0,
    }));

    // Calculate total price
    const totalPrice = services.reduce(
      (sum, service) => sum + service.price,
      0,
    );

    // Fetch payment method from transactions if this is a completed appointment
    let paymentMethod = null;
    if (appointmentData.status === "completed") {
      console.log(
        "Fetching payment method for completed appointment:",
        appointmentId,
      );

      // Debug: Log the SQL query we're about to execute
      console.log(
        `SELECT payment_method FROM transactions WHERE appointment_id = '${appointmentId}' ORDER BY created_at DESC LIMIT 1`,
      );

      const { data: transactionData, error: transactionError } = await supabase
        .from("transactions")
        .select("payment_method")
        .eq("appointment_id", appointmentId)
        .order("created_at", { ascending: false })
        .limit(1);

      console.log(
        "Transaction data for appointment:",
        appointmentId,
        transactionData,
      );

      if (!transactionError && transactionData && transactionData.length > 0) {
        paymentMethod = transactionData[0].payment_method;
        console.log("Found payment method:", paymentMethod);
      } else {
        console.log(
          "No payment method found in transactions. Error:",
          transactionError,
        );

        // Debug: Let's check if there are any transactions for this appointment at all
        const { data: allTransactions, error: allTransactionsError } =
          await supabase
            .from("transactions")
            .select("*")
            .eq("appointment_id", appointmentId);

        console.log("All transactions for this appointment:", allTransactions);
        console.log("Error fetching all transactions:", allTransactionsError);
      }
    }

    // Create the result object with the payment method
    const result = {
      ...appointmentData,
      client_name: `${appointmentData.clients?.first_name || ""} ${
        appointmentData.clients?.last_name || ""
      }`.trim(),
      staff_name: staffName,
      services: services,
      price: totalPrice,
      paymentMethod: paymentMethod,
    };

    // Debug: Check if paymentMethod is correctly set in the result
    console.log("Payment method value being returned:", paymentMethod);
    console.log("Returning appointment with payment method:", result);

    // Ensure the paymentMethod property is explicitly set
    if (paymentMethod) {
      result.paymentMethod = paymentMethod;
    }
    return result;
  } catch (error) {
    console.error("Error in getAppointment:", error);
    return null;
  }
};
