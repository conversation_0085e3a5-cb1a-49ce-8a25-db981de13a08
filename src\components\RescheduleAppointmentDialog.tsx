import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { createLocalDateTimeString } from '../utils/dateUtils';
import { Calendar, Clock, ArrowLeft, ArrowRight, CheckCircle, User } from 'lucide-react';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { useToast } from './ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { updateAppointmentDate, updateAppointmentStatus, updateAppointmentDateAndStatus, getCommonTimeSlots } from '../services/appointmentService';
import { AppointmentStatus } from '../types/appointment';
import { supabase } from '../contexts/SimpleAuthContext';

interface RescheduleAppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentId: string;
  currentDate: string;
  onRescheduleComplete: () => void;
  staffMembers?: { id: string; name: string }[];
  currentStatus?: AppointmentStatus;
  currentStaffId?: string; // Add this prop
}

export function RescheduleAppointmentDialog({
  isOpen,
  onClose,
  appointmentId,
  currentDate,
  onRescheduleComplete,
  staffMembers = [],
  currentStatus = 'scheduled',
  currentStaffId = '' // Default to empty string
}: RescheduleAppointmentDialogProps) {
  const { toast } = useToast();
  const isPending = currentStatus === 'pending';
  const [date, setDate] = useState(format(new Date(currentDate), 'yyyy-MM-dd'));
  const [time, setTime] = useState(format(new Date(currentDate), 'HH:mm'));
  const [selectedStaff, setSelectedStaff] = useState(currentStaffId || '');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'staff' | 'date' | 'time' | 'confirm'>(isPending ? 'staff' : 'staff');
  const [bookedTimeSlots, setBookedTimeSlots] = useState<string[]>([]);
  const [appointmentDuration, setAppointmentDuration] = useState<number>(30); // Default to 30 minutes
  const [bookedAppointments, setBookedAppointments] = useState<any[]>([]);

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      try {
        const appointmentDate = new Date(currentDate);
        setDate(format(appointmentDate, 'yyyy-MM-dd'));
        setTime(format(appointmentDate, 'HH:mm'));
        console.log('Setting initial date and time:', format(appointmentDate, 'yyyy-MM-dd'), format(appointmentDate, 'HH:mm'));
      } catch (error) {
        console.error('Error parsing date:', error, currentDate);
        // If there's an error parsing the date, use today's date
        setDate(format(new Date(), 'yyyy-MM-dd'));
        setTime(format(new Date(), 'HH:mm'));
      }

      // Initialize with the currentStaffId prop
      setSelectedStaff(currentStaffId || '');

      setStep(isPending ? 'staff' : 'staff');
      setIsSubmitting(false);
      setBookedTimeSlots([]);
    }
  }, [isOpen, currentDate, staffMembers, isPending, currentStaffId]);

  // Fetch booked time slots when date or staff changes
  useEffect(() => {
    const fetchBookedAppointments = async () => {
      if (!date || !selectedStaff) return;

      try {
        // Create start and end of the selected date
        const startDate = `${date}T00:00:00`;
        const endDate = `${date}T23:59:59`;

        // Query to get all appointments for the selected staff on the selected date
        let query = supabase
          .from('appointments')
          .select('appointment_id, appointment_date, duration, staff_id')
          .eq('staff_id', selectedStaff)
          .gte('appointment_date', startDate)
          .lte('appointment_date', endDate)
          .in('status', ['scheduled', 'checked-in', 'pending']); // Only consider active appointments

        // If we're rescheduling (not pending), exclude the current appointment
        if (!isPending && appointmentId) {
          query = query.neq('appointment_id', appointmentId);
        }

        const { data, error } = await query;

        if (error) throw error;

        setBookedAppointments(data || []);
        console.log('Fetched booked appointments:', data);

        // Extract booked time slots based on appointments
        const bookedSlots: string[] = [];
        if (data && data.length > 0) {
          // Process each appointment to determine which time slots are booked
          const allTimeSlots = getCommonTimeSlots();

          allTimeSlots.forEach(slot => {
            const [hours, minutes] = slot.split(':').map(Number);

            // Create a date object for this slot on the selected date
            const slotTime = new Date(date);
            slotTime.setHours(hours, minutes, 0, 0);

            // Create end time (assuming 30-minute slots)
            const slotEndTime = new Date(slotTime);
            slotEndTime.setMinutes(slotEndTime.getMinutes() + 30);

            // Check if this slot overlaps with any appointment
            const isBooked = data.some(appointment => {
              const appointmentStart = new Date(appointment.appointment_date);
              const appointmentEnd = new Date(appointmentStart);
              appointmentEnd.setMinutes(appointmentEnd.getMinutes() + (appointment.duration || 60));

              return (
                (slotTime >= appointmentStart && slotTime < appointmentEnd) || // Slot start time is within appointment
                (slotEndTime > appointmentStart && slotEndTime <= appointmentEnd) || // Slot end time is within appointment
                (slotTime <= appointmentStart && slotEndTime >= appointmentEnd) // Slot completely contains appointment
              );
            });

            if (isBooked) {
              bookedSlots.push(slot);
            }
          });
        }

        setBookedTimeSlots(bookedSlots);
        console.log('Calculated booked time slots:', bookedSlots);
      } catch (error) {
        console.error('Error fetching booked appointments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load availability information.',
          variant: 'destructive',
        });
      }
    };

    fetchBookedAppointments();
  }, [date, selectedStaff, isPending, appointmentId, toast]);

  const handleNext = () => {
    if (step === 'staff') {
      if (!selectedStaff) {
        toast({
          title: 'Select a Staff Member',
          description: 'Please assign a staff member to continue.',
          variant: 'destructive',
        });
        return;
      }
      setStep('date');
    } else if (step === 'date') {
      if (!date) {
        toast({
          title: 'Select a Date',
          description: 'Please select a date to continue.',
          variant: 'destructive',
        });
        return;
      }
      setStep('time');
    } else if (step === 'time') {
      if (!time) {
        toast({
          title: 'Select a Time',
          description: 'Please select a time to continue.',
          variant: 'destructive',
        });
        return;
      }
      setStep('confirm');
    }
  };

  const handleBack = () => {
    if (step === 'date') setStep('staff');
    else if (step === 'time') setStep('date');
    else if (step === 'confirm') setStep('time');
  };

  const handleSubmit = async () => {
    if (!date || !time) {
      toast({
        title: 'Error',
        description: 'Please select both date and time.',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedStaff) {
      toast({
        title: 'Error',
        description: 'Please assign a staff member to this appointment.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Create a date object with the correct local time
      const appointmentDate = createLocalDateTimeString(date, time);
      const appointmentDateTime = new Date(appointmentDate);

      // Check if the appointment date and time is in the past
      const now = new Date();
      if (appointmentDateTime < now) {
        toast({
          title: 'Invalid Date/Time',
          description: 'Cannot reschedule to a past date and time. Please select a future date and time.',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // If this is a pending appointment, update both date and status in a single operation
      if (isPending) {
        // Update both appointment date and status in a single database operation
        const updatedAppointment = await updateAppointmentDateAndStatus(
          appointmentId,
          appointmentDate,
          'scheduled',
          selectedStaff,
          'card' // Default payment method
        );

        if (!updatedAppointment) {
          throw new Error('Failed to update appointment');
        }

        toast({
          title: 'Success',
          description: 'Appointment scheduled and staff assigned successfully.',
        });
      } else {
        // For regular reschedules, just update the date and staff
        const updatedAppointment = await updateAppointmentDate(appointmentId, appointmentDate, selectedStaff);

        if (!updatedAppointment) {
          throw new Error('Failed to update appointment date');
        }

        toast({
          title: 'Success',
          description: 'Appointment rescheduled successfully.',
        });
      }

      onRescheduleComplete();
      onClose();
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      toast({
        title: 'Error',
        description: 'Failed to reschedule appointment. Please try again.',
        variant: 'destructive',
      });
      setIsSubmitting(false);
    }
  };

  // Format date for display
  const formattedDate = date ? format(new Date(date), 'MMMM d, yyyy') : '';
  const formattedTime = time ? format(new Date(`2000-01-01T${time}`), 'h:mm a') : '';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg sm:max-w-[500px] w-full mx-4 p-6 max-h-[90vh] overflow-y-auto">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-center">
            {isPending ? 'Schedule Appointment' : 'Reschedule Appointment'}
          </h2>
        </div>

        {/* Step Indicator */}
        <div className="flex items-center justify-between mb-6">
          {(isPending ? ['staff', 'date', 'time', 'confirm'] : ['date', 'time', 'confirm']).map((s, index, arr) => {
            const isActive = step === s;
            const isPast =
              (step === 'date' && s === 'staff') ||
              (step === 'time' && (s === 'staff' || s === 'date')) ||
              (step === 'confirm' && (s === 'staff' || s === 'date' || s === 'time'));

            let Icon;
            if (s === 'date') Icon = Calendar;
            else if (s === 'time') Icon = Clock;
            else if (s === 'staff') Icon = User;
            else Icon = CheckCircle;

            let stepName;
            if (s === 'date') stepName = 'Date';
            else if (s === 'time') stepName = 'Time';
            else if (s === 'staff') stepName = 'Staff';
            else stepName = 'Confirm';

            return (
              <div key={s} className="flex flex-col items-center relative">
                {/* Connecting line */}
                {index < arr.length - 1 && (
                  <div className={`absolute top-4 left-[50%] w-[calc(100%-2rem)] h-[2px] ${
                    isPast ? 'bg-green-500' : 'bg-gray-200'
                  }`} style={{ transform: 'translateX(50%)' }} />
                )}

                {/* Step circle */}
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center z-10 ${
                    isActive ? 'bg-blue-600 text-white' :
                    isPast ? 'bg-green-500 text-white' :
                    'bg-gray-200 text-gray-500'
                  }`}
                >
                  {isPast ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Icon className="h-4 w-4" />
                  )}
                </div>

                {/* Step name */}
                <span className={`text-xs mt-2 ${
                  isActive ? 'text-blue-600 font-medium' :
                  isPast ? 'text-green-600' :
                  'text-gray-500'
                }`}>
                  {stepName}
                </span>
              </div>
            );
          })}
        </div>

        {/* Step Content */}
        <div className="mb-6">
          {step === 'date' && (
            <div className="space-y-4">
              <div className="flex items-center mb-3">
                <Calendar className="h-5 w-5 mr-2 text-gray-700" />
                <h4 className="font-medium">Select Date</h4>
              </div>

              <div className="grid grid-cols-4 gap-2">
                {/* Generate next 14 days for date selection */}
                {Array.from({ length: 14 }).map((_, index) => {
                  const dateOption = new Date();
                  dateOption.setDate(dateOption.getDate() + index);

                  const value = format(dateOption, 'yyyy-MM-dd');
                  const isSelected = date === value;
                  const isToday = index === 0;

                  // Format date for display
                  const dayName = format(dateOption, 'EEE');
                  const dayNumber = format(dateOption, 'd');
                  const monthName = format(dateOption, 'MMM');
                  const display = isToday
                    ? `${dayName}, ${dayNumber} ${monthName} (Today)`
                    : `${dayName}, ${dayNumber} ${monthName}`;

                  return (
                    <div
                      key={index}
                      onClick={() => setDate(value)}
                      className={`
                        p-2 rounded-md text-center cursor-pointer text-sm transition-colors
                        ${isSelected
                          ? isToday
                            ? 'bg-gray-900 text-white'
                            : 'bg-blue-600 text-white'
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}
                      `}
                    >
                      {dayName},<br/>{dayNumber} {monthName}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {step === 'time' && (
            <div className="space-y-4">
              <div className="flex items-center mb-3">
                <Clock className="h-5 w-5 mr-2 text-gray-700" />
                <h4 className="font-medium">Select Available Time Slot</h4>
              </div>

              <div className="grid grid-cols-4 gap-2 mt-2">
                {/* Generate time slots from 9 AM to 9 PM */}
                {(() => {
                  const slots = [];
                  for (let hour = 9; hour <= 21; hour++) {
                    const hourStr = hour <= 12 ? `${hour}:00` : `${hour-12}:00`;
                    const amPm = hour < 12 ? 'AM' : 'PM';
                    slots.push(`${hourStr} ${amPm}`);

                    if (hour !== 21) { // Don't add :30 for 9 PM
                      const halfHourStr = hour <= 12 ? `${hour}:30` : `${hour-12}:30`;
                      slots.push(`${halfHourStr} ${amPm}`);
                    }
                  }
                  return slots;
                })().map((timeSlot, index) => {
                  // Check if this time slot is in the past
                  const isTimeSlotPast = () => {
                    const now = new Date();
                    const today = format(now, 'yyyy-MM-dd');

                    // Only check for today
                    if (date !== today) return false;

                    // Convert display time (e.g. "9:00 AM") to 24h format for comparison
                    const [hourMin, period] = timeSlot.split(' ');
                    const [hour, minute] = hourMin.split(':');
                    let hour24 = parseInt(hour);
                    if (period === 'PM' && hour24 !== 12) hour24 += 12;
                    if (period === 'AM' && hour24 === 12) hour24 = 0;

                    // Create a date object for the time slot
                    const slotTime = new Date();
                    slotTime.setHours(hour24, parseInt(minute), 0, 0);

                    // Add a buffer (e.g., 30 minutes) to prevent booking too close to current time
                    const bufferTime = new Date();
                    bufferTime.setMinutes(bufferTime.getMinutes() + 30);

                    return slotTime < bufferTime;
                  };

                  // Convert display time to 24h format for database
                  const getTimeValue = (timeSlot: string) => {
                    const [hourMin, period] = timeSlot.split(' ');
                    const [hour, minute] = hourMin.split(':');
                    let hour24 = parseInt(hour);
                    if (period === 'PM' && hour24 !== 12) hour24 += 12;
                    if (period === 'AM' && hour24 === 12) hour24 = 0;

                    return `${hour24.toString().padStart(2, '0')}:${minute}`;
                  };

                  const timeValue = getTimeValue(timeSlot);
                  const isPast = isTimeSlotPast();

                  // Check if this time slot is already booked
                  const isTimeSlotBooked = () => {
                    if (!selectedStaff || bookedTimeSlots.length === 0) return false;

                    // Check if this time value is in the bookedTimeSlots array
                    // First convert 24h format (e.g. "14:30") to the format used in bookedTimeSlots (e.g. "14:30")
                    return bookedTimeSlots.includes(timeValue);
                  };

                  const isBooked = isTimeSlotBooked();
                  const isDisabled = isPast || isBooked;
                  const isSelected = time === timeValue;

                  return (
                    <div
                      key={index}
                      onClick={() => !isDisabled && setTime(timeValue)}
                      className={`
                        p-2 rounded-md text-center transition-colors
                        ${isDisabled
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : isSelected
                            ? 'bg-blue-600 text-white cursor-pointer'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-800 cursor-pointer'}
                        ${isBooked ? 'line-through' : ''}
                      `}
                      title={isBooked ? 'Already booked' : isPast ? 'Time slot has passed' : ''}
                    >
                      {timeSlot}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {step === 'staff' && (
            <div className="space-y-4">
              <div className="flex items-center mb-3">
                <User className="h-5 w-5 mr-2 text-gray-700" />
                <h4 className="font-medium">Assign Staff Member</h4>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto p-1">
                {staffMembers.length > 0 ? (
                  staffMembers.map((staffMember) => (
                    <div
                      key={staffMember.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedStaff === staffMember.id
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/50'
                      }`}
                      onClick={() => setSelectedStaff(staffMember.id)}
                    >
                      <div className="flex items-start">
                        <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                          <User className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium">{staffMember.name}</p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 text-center py-8 text-gray-500">
                    No staff members available.
                  </div>
                )}
              </div>

              {isPending && (
                <div className="bg-purple-50 p-3 rounded-lg border border-purple-100">
                  <p className="text-sm text-purple-800">
                    This is a pending appointment. Assigning a staff member will change the status to scheduled.
                  </p>
                </div>
              )}
            </div>
          )}

          {step === 'confirm' && (
            <div className="space-y-6">
              <div className="flex items-center mb-3">
                <CheckCircle className="h-5 w-5 mr-2 text-gray-700" />
                <h4 className="font-medium">Confirm Details</h4>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
                <h3 className="font-medium text-gray-800 mb-2">Appointment Summary</h3>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                      <User className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Staff</p>
                      <p className="text-sm text-gray-600">
                        {staffMembers.find(s => s.id === selectedStaff)?.name || 'Selected Staff'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                      <Calendar className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Date</p>
                      <p className="text-sm text-gray-600">{formattedDate}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                      <Clock className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Time</p>
                      <p className="text-sm text-gray-600">{formattedTime}</p>
                    </div>
                  </div>
                </div>
              </div>

              {isPending && (
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-purple-500 mr-2" />
                    <p className="text-sm text-purple-800">
                      This appointment will be marked as scheduled after confirmation.
                    </p>
                  </div>
                </div>
              )}

              <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <p className="text-sm text-green-800">
                    {isPending ? 'Ready to schedule this appointment?' : 'Ready to reschedule this appointment?'}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-6">
          {(step !== 'staff' && isPending) || (step !== 'date' && !isPending) ? (
            <Button
              variant="outline"
              onClick={handleBack}
              className="flex items-center"
              disabled={isSubmitting}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}

          {step !== 'confirm' ? (
            <Button
              onClick={handleNext}
              className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center"
            >
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              className="bg-green-600 hover:bg-green-700 text-white flex items-center"
              disabled={isSubmitting}
            >
              {isSubmitting ? (isPending ? 'Scheduling...' : 'Rescheduling...') : (isPending ? 'Confirm Schedule' : 'Confirm Reschedule')}
              <CheckCircle className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
