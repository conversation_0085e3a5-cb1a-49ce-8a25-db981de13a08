export type AppointmentStatus = 'pending' | 'scheduled' | 'checked-in' | 'completed' | 'cancelled';
export interface Service {
  name: string;
  price: number;
  duration?: number;
}

export interface Appointment {
  appointment_id: string;
  salon_id: string;
  client_id: string;
  client_name: string;
  staff_name: string;
  appointment_date: string;
  duration: number;
  status: AppointmentStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
  services: Service[];
  price: number;
}
