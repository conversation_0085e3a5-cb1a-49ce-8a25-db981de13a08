import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { cn } from "../../lib/utils";
import { useToast, Toast as ToastType } from "./use-toast";

interface ToastProps {
  toast: ToastType;
}

export function Toast({ toast }: ToastProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(toast.onDismiss, 300); // Wait for animation to complete
    }, 5000);

    return () => clearTimeout(timer);
  }, [toast]);

  return (
    <div
      className={cn(
        "fixed bottom-4 right-4 z-50 max-w-md rounded-lg shadow-lg transition-all duration-300 ease-in-out",
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2",
        toast.props.variant === "destructive"
          ? "bg-red-50 border border-red-200"
          : "bg-white border border-gray-200"
      )}
    >
      <div className="flex items-start p-4">
        <div className="flex-1 mr-2">
          {toast.props.title && (
            <h3
              className={cn(
                "text-sm font-medium",
                toast.props.variant === "destructive"
                  ? "text-red-800"
                  : "text-gray-900"
              )}
            >
              {toast.props.title}
            </h3>
          )}
          {toast.props.description && (
            <p
              className={cn(
                "mt-1 text-sm",
                toast.props.variant === "destructive"
                  ? "text-red-700"
                  : "text-gray-500"
              )}
            >
              {toast.props.description}
            </p>
          )}
        </div>
        <button
          onClick={() => {
            setIsVisible(false);
            setTimeout(toast.onDismiss, 300);
          }}
          className={cn(
            "rounded-md p-1 transition-colors",
            toast.props.variant === "destructive"
              ? "text-red-400 hover:bg-red-100"
              : "text-gray-400 hover:bg-gray-100"
          )}
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

export function Toaster() {
  const { toasts } = useToast();

  return (
    <>
      {toasts.map((toast) => (
        <Toast key={toast.id} toast={toast} />
      ))}
    </>
  );
}
