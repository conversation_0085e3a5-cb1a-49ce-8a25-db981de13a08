import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Calendar, Users, Scissors, UserCircle, Plus } from 'lucide-react';

export function MobileNavBar() {
  const location = useLocation();

  const navItems = [
    { name: 'Home', href: '/dashboard', icon: Home },
    { name: 'Appointments', href: '/appointments', icon: Calendar },
    { name: 'Clients', href: '/clients', icon: Users },
    { name: 'Services', href: '/services', icon: Scissors },
    { name: 'Staff', href: '/staff', icon: UserCircle },
  ];

  // Determine if we should show the action button based on the current page
  const shouldShowActionButton = location.pathname !== '/dashboard';

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg md:hidden z-40">
      <div className="flex items-center h-14 px-2 bg-white border-t border-gray-100">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.href;

          return (
            <Link
              key={item.name}
              to={item.href}
              className="flex-1 flex items-center justify-center h-full"
              aria-label={item.name}
            >
              <div
                className={`
                  flex items-center justify-center rounded-full w-10 h-10
                  ${isActive
                    ? 'text-indigo-600 bg-indigo-50'
                    : 'text-gray-500 hover:bg-gray-50 hover:text-indigo-500'
                  }
                  transition-all duration-200
                `}
              >
                <Icon className="h-5 w-5" />
              </div>
            </Link>
          );
        })}

        {/* Floating action button for quick add - only show on relevant pages */}
        {shouldShowActionButton && (
          <div className="absolute right-4 -top-6">
            <button
              onClick={() => {
                // Handle different actions based on current page
                if (location.pathname === '/appointments') {
                  // Open appointment booking dialog
                  window.dispatchEvent(new CustomEvent('open-appointment-dialog'));
                } else if (location.pathname === '/clients') {
                  // Open add client dialog
                  window.dispatchEvent(new CustomEvent('open-client-dialog'));
                } else if (location.pathname === '/services') {
                  // Open add service dialog
                  window.dispatchEvent(new CustomEvent('open-service-dialog'));
                } else if (location.pathname === '/staff') {
                  // Open add staff dialog
                  window.dispatchEvent(new CustomEvent('open-staff-dialog'));
                }
              }}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg"
            >
              <Plus className="h-6 w-6" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
