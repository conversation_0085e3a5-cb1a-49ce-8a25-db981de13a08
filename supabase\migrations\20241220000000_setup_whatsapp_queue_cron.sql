-- Enable pg_cron extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create a function to trigger the WhatsApp queue processor
CREATE OR REPLACE FUNCTION public.trigger_whatsapp_queue_processor()
RETURNS void AS $$
BEGIN
  -- Make HTTP request to the queue processor edge function
  PERFORM
    net.http_post(
      current_setting('app.settings.supabase_url') || '/functions/v1/whatsapp-queue-processor',
      '{}',
      jsonb_build_object(
        'Content-Type', 'application/json',
        'Authorization', 'Bearer ' || current_setting('app.settings.service_role_key')
      )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule the function to run every 2 minutes
-- Note: This will only work if pg_cron is properly configured
SELECT cron.schedule(
  'whatsapp-queue-processor',
  '*/2 * * * *',
  'SELECT public.trigger_whatsapp_queue_processor();'
);

-- Create a table to track cron job status (optional, for monitoring)
CREATE TABLE IF NOT EXISTS public.whatsapp_queue_cron_log (
  id SERIAL PRIMARY KEY,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT,
  details JSONB
);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.trigger_whatsapp_queue_processor() TO service_role;
GRANT ALL ON TABLE public.whatsapp_queue_cron_log TO service_role;
