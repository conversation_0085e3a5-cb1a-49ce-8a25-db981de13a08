import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import { Loader2 } from 'lucide-react';
import { Appointment } from '../types/appointment';

interface InvoiceGeneratorProps {
  appointment: {
    id: string;
    client: string;
    services: Array<{ name: string; price: number }>;
    appointmentDate: string;
    stylist: string;
    price: number;
    paymentMethod?: string; // This should match the property name in the appointment object
  };
  salonName: string;
  salonAddress?: string;
  onGenerated?: () => void;
}

export function InvoiceGenerator({
  appointment,
  salonName,
  salonAddress,
  onGenerated
}: InvoiceGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(true);

  // Debug: Log the entire appointment object
  console.log('Full appointment object in InvoiceGenerator:', appointment);
  console.log('Payment method in InvoiceGenerator constructor:', appointment.paymentMethod);

  // Handle both property naming conventions (appointment_id or id)
  const appointmentId = appointment.appointment_id || appointment.id;

  // Ensure we have a valid payment method
  const paymentMethodValue = appointment.paymentMethod || 'card';
  console.log('Payment method value to use:', paymentMethodValue);

  // Format date for display
  const formattedDate = format(new Date(appointment.appointmentDate), 'MMMM d, yyyy');
  const formattedTime = format(new Date(appointment.appointmentDate), 'h:mm a');

  // Generate invoice number and date
  const invoiceNumber = `INV-${appointmentId.substring(0, 8)}`;
  const invoiceDate = format(new Date(), 'MMMM d, yyyy');

  const generateInvoice = () => {
    try {
      setIsGenerating(true);

      // Debug: Log the appointment data to see if paymentMethod is present
      console.log('Generating invoice with appointment data:', appointment);

      // Create a new PDF document
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Set font sizes and styles
      const titleFontSize = 16;
      const headingFontSize = 12;
      const normalFontSize = 10;
      const smallFontSize = 8;

      // Page dimensions
      const pageWidth = 210;
      const margin = 20;
      const contentWidth = pageWidth - (margin * 2);

      // Current Y position for content
      let y = margin;

      // Add salon header
      pdf.setFontSize(titleFontSize);
      pdf.setFont('helvetica', 'bold');
      pdf.text(salonName, margin, y);

      // Add INVOICE text on the right
      pdf.text('INVOICE', pageWidth - margin - pdf.getTextWidth('INVOICE'), y);

      // Add salon address
      if (salonAddress) {
        y += 7;
        pdf.setFontSize(normalFontSize);
        pdf.setFont('helvetica', 'normal');
        pdf.text(salonAddress, margin, y);
      }

      // Add invoice number and date on the right
      y += 7;
      pdf.setFontSize(normalFontSize);
      pdf.text(`#${invoiceNumber}`, pageWidth - margin - pdf.getTextWidth(`#${invoiceNumber}`), y);
      y += 5;
      pdf.text(`Date: ${invoiceDate}`, pageWidth - margin - pdf.getTextWidth(`Date: ${invoiceDate}`), y);

      // Add client info
      y += 15;
      pdf.setFontSize(headingFontSize);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Bill To:', margin, y);
      y += 7;
      pdf.setFontSize(normalFontSize);
      pdf.setFont('helvetica', 'normal');
      pdf.text(appointment.client, margin, y);

      // Add appointment details
      y += 15;
      pdf.setFontSize(headingFontSize);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Appointment Details:', margin, y);
      y += 7;
      pdf.setFontSize(normalFontSize);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Date: ${formattedDate}`, margin, y);
      y += 5;
      pdf.text(`Time: ${formattedTime}`, margin, y);
      y += 5;
      pdf.text(`Stylist: ${appointment.stylist}`, margin, y);

      // Add services table
      y += 15;
      pdf.setFontSize(headingFontSize);
      pdf.setFont('helvetica', 'bold');

      // Table header
      pdf.setFillColor(240, 240, 240);
      pdf.rect(margin, y, contentWidth, 8, 'F');
      pdf.text('Service', margin + 5, y + 5.5);
      pdf.text('Price', pageWidth - margin - 25, y + 5.5);

      // Table rows for each service
      y += 8;
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(normalFontSize);

      if (appointment.services && appointment.services.length > 0) {
        appointment.services.forEach((service, index) => {
          pdf.text(service.name, margin + 5, y + 5);
          // Display price without any prefix, just the number with currency
          const priceText = "₹" + service.price.toFixed(2);
          pdf.text(priceText, pageWidth - margin - 25, y + 5);

          // Add a line after each service except the last one
          y += 10;
          if (index < appointment.services.length - 1) {
            pdf.line(margin, y, margin + contentWidth, y); // Horizontal line
          }
        });
      } else {
        // Fallback if no services array is provided
        pdf.text("Service", margin + 5, y + 5);
        const priceText = "₹" + appointment.price.toFixed(2);
        pdf.text(priceText, pageWidth - margin - 25, y + 5);
        y += 10;
      }

      // Draw table borders
      pdf.setDrawColor(200, 200, 200);
      pdf.line(margin, y, margin + contentWidth, y); // Horizontal line
      y += 10;
      pdf.line(margin, y, margin + contentWidth, y); // Horizontal line

      // Subtotal, tax, and total
      y += 5;
      pdf.setFont('helvetica', 'normal');

      // The stored price is the subtotal (sum of service prices)
      const subtotal = appointment.price;
      pdf.text('Subtotal', pageWidth - margin - 60, y);
      const subtotalText = "₹" + subtotal.toFixed(2);
      pdf.text(subtotalText, pageWidth - margin - 25, y);

      y += 5;
      // Calculate tax amount (10% of subtotal)
      const taxAmount = subtotal * 0.1;
      pdf.text('Tax (10%)', pageWidth - margin - 60, y);
      const taxText = "₹" + taxAmount.toFixed(2);
      pdf.text(taxText, pageWidth - margin - 25, y);

      y += 5;
      // Calculate total (subtotal + tax)
      const total = subtotal + taxAmount;
      pdf.setFont('helvetica', 'bold');
      pdf.text('Total', pageWidth - margin - 60, y);
      const totalText = "₹" + total.toFixed(2);
      pdf.text(totalText, pageWidth - margin - 25, y);

      // Payment info
      y += 15;
      pdf.setFontSize(headingFontSize);
      pdf.text('Payment Information:', margin, y);
      y += 7;
      pdf.setFontSize(normalFontSize);
      pdf.setFont('helvetica', 'normal');
      pdf.text('Payment Status: Paid', margin, y);
      y += 5;

      // Use the payment method from the appointment if available, otherwise use a default
      console.log('Payment method from appointment in InvoiceGenerator:', appointment.paymentMethod);
      console.log('Full appointment object in payment section:', appointment);

      // Use the paymentMethodValue we defined earlier
      let paymentMethodDisplay = 'Cash/Card';
      if (paymentMethodValue) {
        if (paymentMethodValue === 'card') {
          paymentMethodDisplay = 'Credit Card';
        } else if (paymentMethodValue === 'cash') {
          paymentMethodDisplay = 'Cash';
        } else if (paymentMethodValue === 'upi') {
          paymentMethodDisplay = 'UPI';
        } else {
          paymentMethodDisplay = paymentMethodValue.charAt(0).toUpperCase() + paymentMethodValue.slice(1);
        }
        console.log('Payment method found, setting display to:', paymentMethodDisplay);
      } else {
        console.log('No payment method found in appointment object');
      }

      console.log('Final payment method display:', paymentMethodDisplay);

      pdf.text(`Payment Method: ${paymentMethodDisplay}`, margin, y);

      // Add transaction ID
      y += 5;
      pdf.text(`Transaction ID: TXN${Math.floor(Math.random() * 1000000)}`, margin, y);

      // Thank you note
      y += 15;
      pdf.setTextColor(100, 100, 100);
      pdf.setFontSize(normalFontSize);
      pdf.text('Thank you for your business!', pageWidth / 2, y, { align: 'center' });
      y += 5;
      pdf.text('We look forward to seeing you again soon.', pageWidth / 2, y, { align: 'center' });

      // Footer
      y = 270; // Position at bottom of page
      pdf.setFontSize(smallFontSize);
      pdf.setTextColor(150, 150, 150);
      pdf.text('Powered by Salon Orbit Lite', pageWidth / 2, y, { align: 'center' });

      // Save the PDF
      pdf.save(`Invoice-${appointmentId}.pdf`);

      setIsGenerating(false);

      if (onGenerated) {
        onGenerated();
      }
    } catch (error) {
      console.error('Error generating invoice:', error);
      setIsGenerating(false);

      if (onGenerated) {
        onGenerated();
      }
    }
  };

  useEffect(() => {
    // Start the generation process after a short delay
    const timer = setTimeout(() => {
      generateInvoice();
    }, 300);

    return () => clearTimeout(timer);
  }, []);



  return (
    <div>
      {isGenerating && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white p-6 rounded-lg shadow-lg flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600 mb-4" />
            <p className="text-gray-700">Generating invoice...</p>
          </div>
        </div>
      )}
    </div>
  );
}
