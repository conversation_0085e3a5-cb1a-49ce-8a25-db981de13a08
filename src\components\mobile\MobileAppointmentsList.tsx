import React from 'react';
import { Calendar, Clock, User, IndianRupee, Edit2, Trash2, CheckCircle, Check, X } from 'lucide-react';
import { Appointment } from '../../services/salonService';
import { Button } from '../ui/button';
import { format } from 'date-fns';

interface MobileAppointmentsListProps {
  appointments: Appointment[];
  onEdit: (appointment: Appointment) => void;
  onCancel: (id: string) => void;
  onCheckIn: (id: string) => void;
  onCheckOut: (id: string) => void;
  loading: boolean;
  searchQuery: string;
}

export function MobileAppointmentsList({
  appointments,
  onEdit,
  onCancel,
  onCheckIn,
  onCheckOut,
  loading,
  searchQuery
}: MobileAppointmentsListProps) {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-10 bg-gradient-to-b from-white to-indigo-50">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 px-4 text-center bg-gradient-to-b from-white to-indigo-50">
        <div className="h-16 w-16 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center mb-4 shadow-sm">
          <Calendar className="h-8 w-8 text-indigo-400" />
        </div>
        <p className="text-gray-700 font-medium mb-2">
          {searchQuery ? 'No appointments match your search' : 'No appointments found'}
        </p>
        <p className="text-gray-500 text-sm mb-4">
          {!searchQuery && 'Book your first appointment to get started'}
        </p>
      </div>
    );
  }

  // Helper function to format date
  const formatAppointmentDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMM dd, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Helper function to format time
  const formatAppointmentTime = (timeString: string) => {
    try {
      // Assuming timeString is in format "HH:MM:SS"
      const [hours, minutes] = timeString.split(':');
      const hour = parseInt(hours, 10);
      const ampm = hour >= 12 ? 'PM' : 'AM';
      const hour12 = hour % 12 || 12;
      return `${hour12}:${minutes} ${ampm}`;
    } catch (error) {
      return timeString;
    }
  };

  // Group appointments by date
  const groupedAppointments: { [key: string]: Appointment[] } = {};
  appointments.forEach(appointment => {
    const date = formatAppointmentDate(appointment.appointment_date);
    if (!groupedAppointments[date]) {
      groupedAppointments[date] = [];
    }
    groupedAppointments[date].push(appointment);
  });

  // Sort appointments within each date group by time
  Object.keys(groupedAppointments).forEach(date => {
    groupedAppointments[date].sort((a, b) => {
      // Extract time from appointment_date
      const timeA = new Date(a.appointment_date).getTime();
      const timeB = new Date(b.appointment_date).getTime();
      return timeA - timeB; // Sort in ascending order (earlier times first)
    });
  });

  // Get current date for comparison
  const now = new Date();
  const today = format(now, 'MMM dd, yyyy');

  return (
    <div className="divide-y divide-gray-100 bg-gradient-to-b from-white to-indigo-50/30">
      {Object.entries(groupedAppointments)
        // Sort dates with today and future dates first (in chronological order),
        // then past dates (in reverse chronological order)
        .sort(([dateA], [dateB]) => {
          const dateAObj = new Date(dateA);
          const dateBObj = new Date(dateB);
          const isDateAToday = format(dateAObj, 'MMM dd, yyyy') === today;
          const isDateBToday = format(dateBObj, 'MMM dd, yyyy') === today;
          const isDateAFuture = dateAObj >= now;
          const isDateBFuture = dateBObj >= now;
          
          // Today comes first
          if (isDateAToday && !isDateBToday) return -1;
          if (!isDateAToday && isDateBToday) return 1;
          
          // Then future dates in chronological order
          if (isDateAFuture && isDateBFuture) return dateAObj.getTime() - dateBObj.getTime();
          if (isDateAFuture && !isDateBFuture) return -1;
          if (!isDateAFuture && isDateBFuture) return 1;
          
          // Then past dates in reverse chronological order
          return dateBObj.getTime() - dateAObj.getTime();
        })
        .map(([date, dateAppointments]) => (
        <div key={date} className="py-2">
          <div className="px-4 py-2 bg-gradient-to-r from-indigo-100/50 to-purple-100/50 font-medium text-gray-700">
            {date}
          </div>
          <div className="divide-y divide-gray-100">
            {dateAppointments.map((appointment) => (
              <div
                key={appointment.appointment_id}
                className="p-4 hover:bg-white/80 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="h-10 w-10 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center text-indigo-600 shadow-sm mr-3">
                        <Clock className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {formatAppointmentTime(appointment.start_time)} - {formatAppointmentTime(appointment.end_time)}
                        </h3>
                        <div className="flex items-center mt-1 text-sm text-gray-600">
                          <User className="h-3.5 w-3.5 mr-1 text-indigo-400" />
                          {appointment.client_name || 'Client'}
                        </div>
                      </div>
                    </div>

                    <div className="mt-3 ml-13 space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {appointment.services && appointment.services.map((service, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-50 text-indigo-700"
                          >
                            {service.name}
                          </span>
                        ))}
                      </div>

                      {appointment.total_price && (
                        <div className="flex items-center text-sm font-medium text-gray-800">
                          <IndianRupee className="h-3.5 w-3.5 mr-1 text-indigo-400" />
                          {appointment.total_price}
                        </div>
                      )}

                      <div className="flex items-center text-xs text-gray-500">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          appointment.status === 'completed' ? 'bg-green-100 text-green-800' :
                          appointment.status === 'checked-in' ? 'bg-blue-100 text-blue-800' :
                          appointment.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          appointment.status === 'pending' ? 'bg-purple-100 text-purple-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {appointment.status === 'completed' ? 'Completed' :
                           appointment.status === 'checked-in' ? 'Checked In' :
                           appointment.status === 'cancelled' ? 'Cancelled' :
                           appointment.status === 'pending' ? 'Pending' :
                           appointment.status === 'scheduled' ? 'Scheduled' :
                           'Booked'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-1">
                    {appointment.status !== 'cancelled' && appointment.status !== 'checked_out' && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                          onClick={() => onEdit(appointment)}
                        >
                          <Edit2 className="h-4 w-4 text-indigo-500" />
                        </Button>

                        {appointment.status === 'scheduled' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                            onClick={() => onCheckIn(appointment.appointment_id)}
                          >
                            <Check className="h-4 w-4 text-blue-500" />
                          </Button>
                        )}

                        {appointment.status === 'checked-in' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                            onClick={() => onCheckOut(appointment.appointment_id)}
                          >
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                          onClick={() => onCancel(appointment.appointment_id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-400" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
