# WhatsApp Integration Guide

This guide explains how to set up and troubleshoot the WhatsApp integration in Salon Orbit Lite.

## Overview

Salon Orbit Lite integrates with WhatsApp to send automated notifications to clients for:

- Appointment confirmations
- Appointment reminders
- Appointment rescheduling
- Appointment cancellations
- Check-in confirmations
- Checkout/billing information

## Setup Instructions

### 1. Enable WhatsApp in Settings

1. Navigate to **Settings** in the application
2. Find the **WhatsApp Integration** section
3. Toggle the switch to enable WhatsApp notifications
4. Click the **Connect WhatsApp** button

### 2. Connect Your WhatsApp Account

1. A QR code will be displayed on the screen
2. Open WhatsApp on your phone
3. Go to **Menu** > **Linked Devices** > **Link a Device**
4. Scan the QR code displayed in Salon Orbit Lite
5. Wait for the connection confirmation

### 3. Test the Connection

After connecting:

1. Create a test appointment
2. Check that the client receives a WhatsApp notification
3. If no notification is received, see the troubleshooting section below

## Troubleshooting

### Common Issues

#### 1. WhatsApp Not Connected

**Symptoms:**
- Clients are not receiving notifications
- "WhatsApp connection not authenticated" error in logs

**Solutions:**
- Go to Settings and check if WhatsApp shows as connected
- If not connected, click "Connect WhatsApp" and scan the QR code again
- Make sure your phone has an active internet connection
- Ensure WhatsApp is running on your phone

#### 2. Connection Lost

**Symptoms:**
- Notifications were working but suddenly stopped
- "Authentication error" in logs

**Causes:**
- Phone battery died
- WhatsApp was logged out
- Internet connection issues
- WhatsApp was logged in on another device

**Solutions:**
- Go to Settings > WhatsApp Integration
- Click "Connect WhatsApp" to generate a new QR code
- Scan the new QR code with your phone

#### 3. Phone Number Format Issues

**Symptoms:**
- Some clients receive notifications, others don't
- "Invalid phone number" errors in logs

**Solutions:**
- Ensure all client phone numbers are exactly 10 digits
- The system automatically adds the country code (91) for Indian numbers
- Edit client profiles to correct any invalid phone numbers

#### 4. WhatsApp Battery Optimization

**Symptoms:**
- Notifications work initially but stop after a while
- Connection drops frequently

**Solutions:**
- On your phone, disable battery optimization for WhatsApp:
  - **Android:** Settings > Apps > WhatsApp > Battery > Don't optimize
  - **iPhone:** Make sure WhatsApp has background app refresh enabled

### Advanced Troubleshooting

#### Checking Connection Status

To manually check if WhatsApp is connected:

1. Go to Settings > WhatsApp Integration
2. If it shows "WhatsApp Connected" with a green checkmark, the connection is active
3. If not, click "Connect WhatsApp" to reconnect

#### Reconnecting WhatsApp

If you need to reconnect WhatsApp:

1. Go to Settings > WhatsApp Integration
2. Click "Connect WhatsApp"
3. A new QR code will be generated
4. Scan the QR code with your phone
5. Wait for the "WhatsApp Connected" confirmation

#### Checking Logs

If you're experiencing persistent issues:

1. Contact support with the following information:
   - Time when the issue occurred
   - Client phone number (if applicable)
   - Type of notification that failed (appointment creation, cancellation, etc.)
   - Screenshot of any error messages

## Best Practices

For reliable WhatsApp notifications:

1. **Keep your phone powered on** and connected to the internet
2. **Disable battery optimization** for WhatsApp
3. **Don't log out** of WhatsApp on your phone
4. **Don't use** the same WhatsApp account on multiple devices
5. **Regularly check** the connection status in Settings

## FAQ

**Q: Can I use my personal WhatsApp account?**  
A: Yes, but we recommend using a dedicated phone/number for business purposes.

**Q: Will clients see my personal WhatsApp profile?**  
A: Yes, clients will see your WhatsApp profile picture and status.

**Q: Can I customize the notification messages?**  
A: Not currently. The system uses predefined templates for different notification types.

**Q: Do clients need to have WhatsApp installed?**  
A: Yes, clients must have WhatsApp installed on their phones to receive notifications.

**Q: Is there a limit to how many messages I can send?**  
A: WhatsApp may impose limits on message volume. For high-volume usage, consider upgrading to WhatsApp Business API.

**Q: What happens if a client doesn't have WhatsApp?**  
A: The system will attempt to send the message, but it won't be delivered. No error will be shown in the application.

## Support

If you continue to experience issues with WhatsApp integration after trying the troubleshooting steps, please contact support with detailed information about the problem.
