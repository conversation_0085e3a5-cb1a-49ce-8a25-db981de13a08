import React, { useState } from 'react';
import { format } from 'date-fns';
import { formatDateForDisplay, formatTimeForDisplay } from '../utils/dateUtils';
import { Calendar, Clock, User, Scissors, CheckCircle, XCircle, AlertCircle, FileText, CreditCard } from 'lucide-react';
import { IndianRupee } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { AppointmentStatus } from '../types/appointment';
import { CheckoutDialog } from './CheckoutDialog';
import { InvoiceGenerator } from './InvoiceGenerator';
import { RescheduleAppointmentDialog } from './RescheduleAppointmentDialog';
import { updateAppointmentServices, updateAppointmentStatus, updateAppointmentServicesAndStatus } from '../services/appointmentService';

interface AppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  appointment: {
    id: string;
    client: string;
    services: Array<{ name: string; price: number }>;
    appointmentDate: string; // ISO format timestamp
    stylist: string;
    status: AppointmentStatus;
    price: number;
    paymentMethod?: string;
  };
  onStatusChange: (status: AppointmentStatus) => void;
  onReschedule: () => void;
  onCancel: () => void;
  staffMembers?: { id: string; name: string }[];
  services?: { id: string; name: string; price: number }[];
}

export function AppointmentDialog({
  isOpen,
  onClose,
  appointment,
  onStatusChange,
  onReschedule,
  onCancel,
  staffMembers = [],
  services = [],
}: AppointmentDialogProps) {
  const [showCheckoutDialog, setShowCheckoutDialog] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showInvoice, setShowInvoice] = useState(false);
  const [showRescheduleDialog, setShowRescheduleDialog] = useState(false);
  const [salonInfo, setSalonInfo] = useState({ name: 'Salon Orbit', address: '' });
  const [appointmentState, setAppointment] = useState(appointment);

  // Fetch salon information and sync appointment state
  React.useEffect(() => {
    const fetchSalonInfo = async () => {
      try {
        // This would normally come from your API or context
        // For now, we'll use a placeholder
        setSalonInfo({
          name: 'Salon Orbit',
          address: '123 Beauty Lane, Style City, SC 12345'
        });
      } catch (error) {
        console.error('Error fetching salon info:', error);
      }
    };

    fetchSalonInfo();

    // Debug: Log the appointment prop to see if it has the payment method
    console.log('Appointment prop received in useEffect:', appointment);
    console.log('Payment method in appointment prop:', appointment.paymentMethod);

    // Sync appointmentState with appointment prop
    // Ensure we preserve all properties, including paymentMethod
    setAppointment({
      ...appointment,
      paymentMethod: appointment.paymentMethod // Explicitly set paymentMethod
    });
  }, [appointment]);

  const formatDate = (dateString: string) => {
    return formatDateForDisplay(dateString);
  };

  const formatTime = (dateString: string) => {
    return formatTimeForDisplay(dateString);
  };

  const getStatusBadge = (status: AppointmentStatus) => {
    const variants = {
      pending: 'outline',
      scheduled: 'default',
      'checked-in': 'warning',
      completed: 'success',
      cancelled: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const handleCheckout = () => {
    setShowCheckoutDialog(true);
  };

  const handleCheckoutComplete = async (updatedServices, selectedStaffMap, paymentMethod = 'card') => {
    try {
      console.log('Checkout completed with services:', updatedServices);
      console.log('Staff assignments:', selectedStaffMap);
      console.log('Payment method received from checkout:', paymentMethod);

      // Validate payment method
      if (!paymentMethod || (paymentMethod !== 'card' && paymentMethod !== 'cash' && paymentMethod !== 'upi')) {
        console.warn('Invalid payment method received:', paymentMethod);
        console.log('Defaulting to card payment method');
        paymentMethod = 'card';
      }

      // Map the services to the format expected by updateAppointmentServices
      const servicesToUpdate = updatedServices.map(service => {
        console.log('Processing service for update:', service);
        return {
          name: service.name,
          price: service.price,
          // If this is an original service, include its ID
          serviceId: service.originalId || undefined,
          // Include the staff assignment
          staffId: selectedStaffMap[service.id]
        };
      });

      // Update both appointment services and status in a single operation to prevent conflicts
      const completedAppointment = await updateAppointmentServicesAndStatus(
        appointment.id,
        servicesToUpdate,
        'completed',
        paymentMethod
      );

      if (completedAppointment) {
        console.log('Successfully updated appointment services and status:', completedAppointment);
        console.log('Payment method to set in local state:', paymentMethod);

        // Update the local appointment state with the updated data
        // This ensures the UI shows the updated services
        const updatedState = {
          ...appointment,
          services: completedAppointment.services || [],
          price: completedAppointment.price || 0,
          status: 'completed',
          paymentMethod: paymentMethod // Use the payment method from checkout
        };

        console.log('Setting appointment state to:', updatedState);
        setAppointment(updatedState);

        // Notify the parent component
        onStatusChange('completed');

        // Close the dialogs
        setShowCheckoutDialog(false);
        onClose(); // Close the appointment dialog as well
      } else {
        console.error('Failed to update appointment services and status');
        alert('Failed to update appointment. Please try again.');
      }
    } catch (error) {
      console.error('Error in handleCheckoutComplete:', error);
      alert('An error occurred while completing the checkout. Please try again.');
    }
  };

  // Debug logging
  console.log('Staff members in AppointmentDialog:', staffMembers);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Appointment Details</h2>
            {getStatusBadge(appointmentState.status)}
          </div>

          {appointmentState.status === 'completed' && (
            <div className="bg-green-50 p-4 rounded-lg mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <p className="text-green-700 font-medium">This appointment has been completed</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700"
                  onClick={() => setShowInvoice(true)}
                >
                  <FileText className="mr-1 h-4 w-4" />
                  Invoice
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div className="flex items-start">
              <User className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Client</p>
                <p className="text-sm text-gray-500">{appointmentState.client}</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-sm text-gray-700">Services</h4>
              <div className="space-y-2">
                {appointmentState.services.map((service, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span>{service.name}</span>
                    <span>₹{service.price.toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>Total</span>
                  <span>₹{appointmentState.price.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="flex items-start">
              <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Date</p>
                <p className="text-sm text-gray-500">{formatDate(appointmentState.appointmentDate)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Clock className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Time</p>
                <p className="text-sm text-gray-500">{formatTime(appointmentState.appointmentDate)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <User className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Stylist</p>
                <p className="text-sm text-gray-500">{appointmentState.stylist}</p>
              </div>
            </div>

            <div className="flex items-start">
              <IndianRupee className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900">Price Details</p>
                <div className="text-sm text-gray-500">
                  {/* The stored price is the subtotal (sum of service prices) */}
                  {(() => {
                    const subtotal = appointmentState.price || 0;
                    const tax = subtotal * 0.1; // Calculate tax as 10% of subtotal
                    const total = subtotal + tax; // Calculate total as subtotal + tax

                    return (
                      <>
                        <div className="flex justify-between">
                          <span>Subtotal:</span>
                          <span>₹{subtotal.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax (10%):</span>
                          <span>₹{tax.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between font-medium">
                          <span>Total:</span>
                          <span>₹{total.toFixed(2)}</span>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>

            {appointmentState.status === 'completed' && (
              <div className="flex items-start">
                <CreditCard className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Payment Method</p>
                  <p className="text-sm text-gray-500">
                    {/* Debug: Log the payment method */}
                    {console.log('Payment method in AppointmentDialog:', appointmentState.paymentMethod)}
                    {(() => {
                      // Ensure we have a valid payment method
                      const paymentMethod = appointmentState.paymentMethod || 'card';
                      console.log('Payment method to display:', paymentMethod);

                      if (paymentMethod === 'card') {
                        return 'Credit Card';
                      } else if (paymentMethod === 'cash') {
                        return 'Cash';
                      } else if (paymentMethod === 'upi') {
                        return 'UPI';
                      } else {
                        return paymentMethod.charAt(0).toUpperCase() + paymentMethod.slice(1);
                      }
                    })()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-3">
          {appointmentState.status === 'pending' && (
            <Button
              onClick={() => setShowRescheduleDialog(true)}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              <User className="mr-2 h-4 w-4" />
              Schedule & Assign Staff
            </Button>
          )}

          {appointmentState.status === 'scheduled' && (
            <Button
              onClick={() => onStatusChange('checked-in')}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              <CheckCircle className="mr-2 h-4 w-4" />
              Check In
            </Button>
          )}

          {appointmentState.status === 'checked-in' && (
            <Button
              onClick={handleCheckout}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <IndianRupee className="mr-2 h-4 w-4" />
              Checkout
            </Button>
          )}

          {(appointmentState.status === 'scheduled' || appointmentState.status === 'checked-in' || appointmentState.status === 'pending') && (
            <>
              {appointmentState.status !== 'pending' && (
                <Button
                  onClick={() => setShowRescheduleDialog(true)}
                  variant="outline"
                  className="w-full"
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  Reschedule
                </Button>
              )}

              <Button
                onClick={() => setShowCancelConfirm(true)}
                variant="outline"
                className="w-full text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 hover:bg-red-50"
              >
                <XCircle className="mr-2 h-4 w-4" />
                Cancel Appointment
              </Button>
            </>
          )}

          <Button
            onClick={onClose}
            variant="outline"
            className="w-full"
          >
            Close
          </Button>
        </div>
      </div>

      {/* Checkout Dialog */}
      {showCheckoutDialog && (
        <CheckoutDialog
          isOpen={showCheckoutDialog}
          onClose={() => setShowCheckoutDialog(false)}
          onComplete={handleCheckoutComplete}
          initialServices={
            appointmentState.services && appointmentState.services.length > 0
              ? appointmentState.services.map((service, index) => ({
                  id: index + 1,
                  name: service.name,
                  price: service.price,
                  quantity: 1,
                  // Store the original service ID if available
                  originalId: service.id || undefined,
                }))
              : [
                  {
                    id: 1,
                    name: "Service",
                    price: appointmentState.price || 0,
                    quantity: 1,
                  },
                ]
          }
          availableServices={services.length > 0
            ? services.map((service, index) => ({
                id: parseInt(service.id) || index + 1,
                name: service.name,
                price: service.price,
                quantity: 0
              }))
            : [
                { id: 1, name: 'Haircut & Style', price: 60, quantity: 0 },
                { id: 2, name: 'Color Treatment', price: 120, quantity: 0 },
                { id: 3, name: 'Manicure', price: 40, quantity: 0 },
                { id: 4, name: 'Pedicure', price: 50, quantity: 0 },
              ]
          }
          availableStaff={
            staffMembers && staffMembers.length > 0
              ? staffMembers.map((staff, index) => ({
                  id: index + 1, // Use index + 1 as numeric ID for the dropdown
                  originalId: staff.id, // Keep the original ID for reference
                  name: staff.name
                }))
              : [
                  { id: 1, name: 'John Doe' },
                  { id: 2, name: 'Jane Smith' },
                ]
          }
        />
      )}

      {/* Cancel Confirmation Dialog */}
      {showCancelConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setShowCancelConfirm(false)}
          />
          <div className="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold mb-2">Cancel Appointment?</h2>
            <p className="text-gray-600 mb-6">
              Are you sure you want to cancel this appointment? This action cannot be undone.
            </p>
            <div className="flex justify-center space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowCancelConfirm(false)}
              >
                No, Keep It
              </Button>
              <Button
                className="bg-red-600 hover:bg-red-700 text-white"
                onClick={() => {
                  setShowCancelConfirm(false);
                  onCancel();
                }}
              >
                Yes, Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Invoice Generator */}
      {showInvoice && (
        <>
          {/* Debug: Log the appointment state before passing to InvoiceGenerator */}
          {console.log('Appointment state before passing to InvoiceGenerator:', appointmentState)}
          <InvoiceGenerator
            appointment={{
              id: appointmentState.id,
              appointment_id: appointmentState.appointment_id,
              client: appointmentState.client,
              services: appointmentState.services,
              appointmentDate: appointmentState.appointmentDate,
              stylist: appointmentState.stylist,
              price: appointmentState.price,
              // Explicitly set the payment method
              paymentMethod: appointmentState.paymentMethod || 'card'
            }}
            salonName={salonInfo.name}
            salonAddress={salonInfo.address}
            onGenerated={() => setShowInvoice(false)}
          />
        </>
      )}

      {/* Reschedule Dialog */}
      {showRescheduleDialog && (
        <RescheduleAppointmentDialog
          isOpen={showRescheduleDialog}
          onClose={() => setShowRescheduleDialog(false)}
          appointmentId={appointmentState.id}
          currentDate={appointmentState.appointmentDate}
          staffMembers={staffMembers}
          currentStatus={appointmentState.status}
          currentStaffId={appointmentState.staffId} // Pass the current staff ID
          onRescheduleComplete={() => {
            setShowRescheduleDialog(false);
            // Close the current dialog and refresh the appointments list
            onClose();
            // Notify parent that appointment was rescheduled
            onReschedule();
          }}
        />
      )}
    </div>
  );
}
