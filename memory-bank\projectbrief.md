Salon POS System – Project Brief
Overview
This project is a minimal billing and appointment management system designed for a single-salon operation. It focuses on fast and streamlined data entry with core features such as appointment scheduling, client management, service catalog management, staff management, and basic checkout. The system is built with a modern, mobile-first, responsive UI and integrates WhatsApp notifications to inform customers about their appointment bookings and billing.

Core Requirements
User Authentication:
Secure login and signup with seamless navigation between the forms.

Default Salon Setup:
Upon signup, a default salon is automatically created for the user, making the system local to a single salon.

Appointment Management:
Book, reschedule, and cancel appointments; track appointment statuses (scheduled, checked-in, completed, cancelled).

Client Management:
Basic CRUD (create, read, update, delete) operations for managing client details.

Service Catalog:
Manage a simple list of services with details like name, price, duration, and description.

Staff Management:
Manage staff profiles (name and role) and assign them to appointments.

Checkout Process:
Process appointments through a streamlined checkout that finalizes sales and generates a simple bill/invoice.

WhatsApp Integration:
Send WhatsApp notifications for appointment confirmations and checkout billing to the client's phone number.

Time Zone Handling:
Store appointment times in UTC and convert them for display based on the user's or business's time zone preference (stored in localStorage).

Key Features
Seamless Login/Signup Flow:

Unified form that allows users to easily switch between login and signup.

Quick signup includes basic user details (email, password, first name, last name) and minimal salon info (Salon Name [required], Address [optional]).

On signup, the default salon is automatically created and linked to the user.

Dashboard:

A central page offering quick action buttons (New Appointment, Add Client) and summary cards (e.g., “Today’s Appointments”, “Recent Invoices”).

Clean, minimal design with unified navigation across pages.

Appointment Management:

A multi-step appointment form that allows:

Client selection or creation (with a prominent “Change Customer” option).

Service selection with duplicate selections increasing the quantity.

Staff assignment from the default salon.

Date/time scheduling using a clear, opaque time picker.

Review and confirmation of appointment details.

Appointment options include actions based on status:

For Scheduled: Reschedule, Check In, Cancel.

For Checked-In: Complete, Reschedule, Cancel.

For Completed: View details/receipt.

For Cancelled: Indicate cancellation.

Basic checkout process finalizes the appointment sale.

Client, Staff & Service Management:

Clients: Simple table view to add, edit, and delete client records.

Staff: Manage staff profiles with minimal information (name and role).

Services: Maintain a service catalog (name, price, duration, description) with a clean list view; extraneous buttons (like an “Add Service” on the dashboard) are omitted or reserved for future features.

Checkout & Billing:

A minimal checkout flow that generates a basic invoice.

The invoice is sent to the client’s phone number via WhatsApp.

No advanced transaction or expense tracking is included in this version.

Technical Stack
Frontend: React + TypeScript with Vite

UI Components: Radix UI, Tailwind CSS

Routing: React Router

State Management: React hooks (with localStorage for persistent settings such as time zone)

Backend: Supabase (for real-time data, authentication, and database management)

Integration: WhatsApp API (for sending appointment and billing notifications)

Database Schema (Simplified Version)
This schema is streamlined for the minimal system, focusing on core functionality:

Users Table
sql
Copy
CREATE TABLE users (
  user_id        UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email          VARCHAR UNIQUE NOT NULL,
  first_name     VARCHAR NOT NULL,
  last_name      VARCHAR NOT NULL,
  phone          VARCHAR,
  role           VARCHAR NOT NULL DEFAULT 'staff' CHECK (role IN ('owner', 'staff')),
  created_at     TIMESTAMP DEFAULT NOW(),
  updated_at     TIMESTAMP DEFAULT NOW(),
  avatar_url     TEXT,
  display_name   VARCHAR,
  location       VARCHAR,
  date_of_birth  DATE,
  salon_id       UUID REFERENCES salons(salon_id),
  joined_date    DATE
);

CREATE TABLE salons (
  salon_id      UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_user_id UUID REFERENCES users(user_id),
  name          VARCHAR NOT NULL,
  address       VARCHAR,
  created_at    TIMESTAMP DEFAULT NOW(),
  updated_at    TIMESTAMP DEFAULT NOW()
);

CREATE TABLE clients (
  client_id      UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id       UUID REFERENCES salons(salon_id) NOT NULL,
  first_name     VARCHAR NOT NULL,
  last_name      VARCHAR NOT NULL,
  phone          VARCHAR NOT NULL,
  email          VARCHAR,
  address        VARCHAR,
  created_at     TIMESTAMP DEFAULT NOW(),
  updated_at     TIMESTAMP DEFAULT NOW()
);

CREATE TABLE appointments (
  appointment_id   UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id         UUID REFERENCES salons(salon_id) NOT NULL,
  client_id        UUID REFERENCES clients(client_id) NOT NULL,
  appointment_date TIMESTAMP NOT NULL,  -- Stored in UTC
  duration         INTEGER,             -- in minutes
  status           VARCHAR NOT NULL CHECK (status IN ('scheduled', 'checked-in', 'completed', 'cancelled', 'no_show')),
  notes            TEXT,
  staff_id         UUID REFERENCES users(user_id),
  created_at       TIMESTAMP DEFAULT NOW(),
  updated_at       TIMESTAMP DEFAULT NOW()
);

CREATE TABLE appointment_services (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appointment_id  UUID REFERENCES appointments(appointment_id) NOT NULL,
  service_id      UUID REFERENCES services(service_id) NOT NULL,
  price           DECIMAL(10,2) NOT NULL,
  created_at      TIMESTAMP DEFAULT NOW(),
  updated_at      TIMESTAMP DEFAULT NOW()
);

CREATE TABLE services (
  service_id    UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id      UUID REFERENCES salons(salon_id),
  name          VARCHAR NOT NULL,
  description   TEXT,
  price         DECIMAL(10,2),
  duration      INTEGER,  -- in minutes
  created_at    TIMESTAMP DEFAULT NOW(),
  updated_at    TIMESTAMP DEFAULT NOW()
);

CREATE TABLE transactions (
  transaction_id   UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  salon_id         UUID REFERENCES salons(salon_id) NOT NULL,
  appointment_id   UUID REFERENCES appointments(appointment_id),
  client_id        UUID REFERENCES clients(client_id),
  user_id          UUID REFERENCES users(user_id) NOT NULL,
  total_amount     DECIMAL(10,2) NOT NULL,
  payment_method   VARCHAR NOT NULL CHECK (payment_method IN ('cash', 'card', 'upi')),
  status           VARCHAR NOT NULL CHECK (status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
  transaction_date TIMESTAMP DEFAULT NOW(),
  payment_reference VARCHAR,
  created_at       TIMESTAMP DEFAULT NOW(),
  updated_at       TIMESTAMP DEFAULT NOW()
);

CREATE TABLE transaction_items (
  item_id         UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id  UUID REFERENCES transactions(transaction_id) NOT NULL,
  item_type       VARCHAR NOT NULL CHECK (item_type IN ('service', 'product')),
  reference_id    UUID NOT NULL,
  description     VARCHAR,
  quantity        INTEGER DEFAULT 1,
  unit_price      DECIMAL(10,2) NOT NULL,
  total_price     DECIMAL(10,2) NOT NULL,
  created_at      TIMESTAMP DEFAULT NOW(),
  updated_at      TIMESTAMP DEFAULT NOW()
);

WhatsApp Integration
Appointment Notifications:
Upon booking an appointment, a WhatsApp message is sent to the client with details (client name, appointment date/time, staff, salon address).

Checkout Billing:
On completing a checkout, a WhatsApp message is sent to the client with invoice details (invoice number, date, services, total amount).

Next Steps
UI Implementation:
Develop a modern, clean, mobile-first UI with responsive design for the following pages:

Login/Signup: Seamless toggle between forms.

Dashboard: Quick actions (New Appointment, Add Client) and summary stats.

Client, Staff, Service, and Appointment Management: Minimal and data-entry–focused.

Checkout: Simplified process for finalizing appointments.

Time Zone Handling:
Ensure all appointment times are stored in UTC and converted to local time based on a time zone setting stored in localStorage.

WhatsApp Notifications:
Integrate with a WhatsApp API to send notifications on appointment bookings and checkout completions.