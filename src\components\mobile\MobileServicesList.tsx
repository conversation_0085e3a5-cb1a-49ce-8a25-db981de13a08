import React from 'react';
import { Sciss<PERSON>, Clock, IndianRupee, Edit2, Trash2, CheckCircle } from 'lucide-react';
import { Service } from '../../services/salonService';
import { Button } from '../ui/button';

interface MobileServicesListProps {
  services: Service[];
  onEdit: (service: Service) => void;
  onActivate: (id: string) => void;
  onDeactivate: (id: string) => void;
  loading: boolean;
  searchQuery: string;
}

export function MobileServicesList({
  services,
  onEdit,
  onActivate,
  onDeactivate,
  loading,
  searchQuery
}: MobileServicesListProps) {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-10 bg-gradient-to-b from-white to-indigo-50">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (services.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 px-4 text-center bg-gradient-to-b from-white to-indigo-50">
        <div className="h-16 w-16 rounded-full bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center mb-4 shadow-sm">
          <Scissors className="h-8 w-8 text-indigo-400" />
        </div>
        <p className="text-gray-700 font-medium mb-2">
          {searchQuery ? 'No services match your search' : 'No services found'}
        </p>
        <p className="text-gray-500 text-sm mb-4">
          {!searchQuery && 'Add your first service to get started'}
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-100 bg-gradient-to-b from-white to-indigo-50/30">
      {services.map((service) => {
        // @ts-ignore - is_active property exists in the API response but not in the type
        const isInactive = service.is_active === false;

        return (
          <div
            key={service.service_id}
            className={`p-4 ${isInactive ? 'opacity-60' : ''} hover:bg-white/80 transition-colors`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full flex items-center justify-center text-indigo-600 shadow-sm">
                  <Scissors className="h-5 w-5" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{service.name}</h3>
                  <div className="flex items-center mt-1 space-x-3">
                    {service.duration && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="h-3.5 w-3.5 mr-1 text-indigo-400" />
                        {service.duration} min
                      </div>
                    )}
                    <div className="flex items-center text-sm font-medium text-gray-800">
                      <IndianRupee className="h-3.5 w-3.5 mr-1 text-indigo-400" />
                      {service.price}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                  onClick={() => onEdit(service)}
                >
                  <Edit2 className="h-4 w-4 text-indigo-500" />
                </Button>

                {isInactive ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                    onClick={() => onActivate(service.service_id)}
                  >
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full bg-white/80 shadow-sm"
                    onClick={() => onDeactivate(service.service_id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-400" />
                  </Button>
                )}
              </div>
            </div>

            {service.description && (
              <p className="text-sm text-gray-600 mt-2 ml-13 bg-white/50 p-2 rounded-md">
                {service.description}
              </p>
            )}
          </div>
        );
      })}
    </div>
  );
}
