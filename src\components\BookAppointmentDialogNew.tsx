import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { supabase } from '../lib/supabase';
import { createLocalDateTimeString } from '../utils/dateUtils';
import { Calendar, Clock, User, Scissors, CalendarCheck, Search, CheckCircle, ArrowLeft, ArrowRight, UserPlus } from 'lucide-react';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { useToast } from './ui/use-toast';
import { Client, Service, StaffMember, addClient, getCurrentUserSalonId } from '../services/salonService';
import { Badge } from './ui/badge';
import { AddClientDialog } from './AddClientDialog';

interface BookAppointmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (appointment: {
    clientId: string;
    serviceIds: string[]; // Changed from serviceId to serviceIds array
    staffId: string;
    appointmentDate: string; // ISO format timestamp
  }) => void;
  clients: Client[];
  services: Service[];
  staff: StaffMember[];
}

type Step = 'client' | 'service' | 'staff' | 'datetime' | 'summary';

export function BookAppointmentDialog({
  isOpen,
  onClose,
  onSubmit,
  clients,
  services,
  staff,
}: BookAppointmentDialogProps) {
  // State for the stepper
  const [currentStep, setCurrentStep] = useState<Step>('client');

  // Form state
  const [selectedClient, setSelectedClient] = useState('');
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectedStaff, setSelectedStaff] = useState('');
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [time, setTime] = useState('');

  // Search state
  const [clientSearch, setClientSearch] = useState('');
  const [serviceSearch, setServiceSearch] = useState('');
  const [staffSearch, setStaffSearch] = useState('');

  // Add client dialog state
  const [showAddClientDialog, setShowAddClientDialog] = useState(false);
  const [clientsList, setClientsList] = useState<Client[]>(clients);

  // Add this new state for storing booked appointments
  const [bookedAppointments, setBookedAppointments] = useState<Array<{
    appointment_id: string;
    appointment_date: string;
    duration: number;
    staff_id: string;
  }>>([]);

  const { toast } = useToast();

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setCurrentStep('client');
      setSelectedClient('');
      setSelectedServices([]);
      setSelectedStaff('');
      setDate(format(new Date(), 'yyyy-MM-dd'));
      setTime('');
      setClientSearch('');
      setServiceSearch('');
      setStaffSearch('');
      setClientsList(clients);
    }
  }, [isOpen, clients]);

  // Filter clients based on search (name or phone)
  const filteredClients = clientsList.filter(client => {
    const searchTerm = clientSearch.toLowerCase();
    const fullName = `${client.first_name} ${client.last_name}`.toLowerCase();
    const phone = client.phone ? client.phone.toLowerCase() : '';

    return fullName.includes(searchTerm) || phone.includes(searchTerm);
  });

  // Filter services based on search
  const filteredServices = services.filter(service => {
    return service.name.toLowerCase().includes(serviceSearch.toLowerCase());
  });

  // Filter staff based on search
  const filteredStaff = staff.filter(staffMember => {
    const fullName = `${staffMember.first_name} ${staffMember.last_name}`.toLowerCase();
    return fullName.includes(staffSearch.toLowerCase());
  });

  // Get selected items for display
  const selectedClientName = clientsList.find(c => c.client_id === selectedClient)
    ? `${clientsList.find(c => c.client_id === selectedClient)?.first_name} ${clientsList.find(c => c.client_id === selectedClient)?.last_name}`
    : '';

  // Replace single service name with multiple services
  const selectedServicesDetails = selectedServices.map(serviceId => {
    const service = services.find(s => s.service_id === serviceId);
    return {
      name: service?.name || '',
      price: service?.price || 0
    };
  });

  const selectedStaffName = staff.find(s => s.user_id === selectedStaff)
    ? `${staff.find(s => s.user_id === selectedStaff)?.first_name} ${staff.find(s => s.user_id === selectedStaff)?.last_name}`
    : '';

  // Handle adding a new client
  const handleAddClient = async (clientData: {
    firstName: string;
    lastName: string;
    email?: string;
    phone: string;
    address?: string
  }) => {
    try {
      // Get the current salon ID
      const salonId = await getCurrentUserSalonId();

      if (!salonId) {
        toast({
          title: 'Error',
          description: 'Could not determine your salon. Please try again later.',
          variant: 'destructive',
        });
        return;
      }

      const newClient = await addClient({
        salon_id: salonId,
        first_name: clientData.firstName,
        last_name: clientData.lastName,
        email: clientData.email || null,
        phone: clientData.phone,
        address: clientData.address || null,
      });

      if (newClient) {
        // Add the new client to the list and select it
        setClientsList([...clientsList, newClient]);
        setSelectedClient(newClient.client_id);
        toast({
          title: 'Success',
          description: 'Client added successfully',
        });
      }
    } catch (error) {
      console.error('Error adding client:', error);
      toast({
        title: 'Error',
        description: 'Failed to add client. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle next step
  const handleNext = () => {
    if (currentStep === 'client') {
      if (!selectedClient) {
        toast({
          title: 'Select a Client',
          description: 'Please select a client to continue.',
          variant: 'destructive',
        });
        return;
      }
      setCurrentStep('service');
    } else if (currentStep === 'service') {
      if (selectedServices.length === 0) {
        toast({
          title: 'Select Services',
          description: 'Please select at least one service to continue.',
          variant: 'destructive',
        });
        return;
      }
      setCurrentStep('staff');
    } else if (currentStep === 'staff') {
      if (!selectedStaff) {
        toast({
          title: 'Select a Staff Member',
          description: 'Please select a staff member to continue.',
          variant: 'destructive',
        });
        return;
      }
      setCurrentStep('datetime');
    } else if (currentStep === 'datetime') {
      if (!date || !time) {
        toast({
          title: 'Select Date and Time',
          description: 'Please select both date and time to continue.',
          variant: 'destructive',
        });
        return;
      }
      setCurrentStep('summary');
    }
  };

  // Handle back step
  const handleBack = () => {
    if (currentStep === 'service') setCurrentStep('client');
    else if (currentStep === 'staff') setCurrentStep('service');
    else if (currentStep === 'datetime') setCurrentStep('staff');
    else if (currentStep === 'summary') setCurrentStep('datetime');
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!selectedClient || selectedServices.length === 0 || !selectedStaff || !date || !time) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    // Create a date object with the correct local time
    const appointmentDate = createLocalDateTimeString(date, time);
    const appointmentDateTime = new Date(appointmentDate);

    // Check if the appointment date and time is in the past
    const now = new Date();
    if (appointmentDateTime < now) {
      toast({
        title: 'Invalid Date/Time',
        description: 'Cannot book appointments in the past. Please select a future date and time.',
        variant: 'destructive',
      });
      return;
    }

    onSubmit({
      clientId: selectedClient,
      serviceIds: selectedServices,
      staffId: selectedStaff,
      appointmentDate,
    });
    onClose();
  };

  // Add handleServiceSelection function
  const handleServiceSelection = (serviceId: string) => {
    setSelectedServices(prevSelected => {
      if (prevSelected.includes(serviceId)) {
        // Remove the service if it's already selected
        return prevSelected.filter(id => id !== serviceId);
      } else {
        // Add the service if it's not selected
        return [...prevSelected, serviceId];
      }
    });
  };

  // Add this effect to fetch booked appointments when date or selected staff changes
  useEffect(() => {
    const fetchBookedAppointments = async () => {
      if (!date || !selectedStaff) return;
      
      // Create start and end of the selected date
      const startDate = `${date}T00:00:00`;
      const endDate = `${date}T23:59:59`;
      
      try {
        const { data, error } = await supabase
          .from('appointments')
          .select('appointment_id, appointment_date, duration, staff_id')
          .eq('staff_id', selectedStaff)
          .gte('appointment_date', startDate)
          .lte('appointment_date', endDate)
          .in('status', ['scheduled', 'checked-in']); // Only consider active appointments
      
        if (error) throw error;
        
        setBookedAppointments(data || []);
      } catch (error) {
        console.error('Error fetching booked appointments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load booked appointments.',
          variant: 'destructive',
        });
      }
    };
    
    fetchBookedAppointments();
  }, [date, selectedStaff, toast]);

  // Render step indicators
  const renderStepIndicators = () => {
    const steps: { key: Step; label: string; icon: any }[] = [
      { key: 'client', label: 'Client', icon: User },
      { key: 'service', label: 'Service', icon: Scissors },
      { key: 'staff', label: 'Staff', icon: CalendarCheck },
      { key: 'datetime', label: 'Date & Time', icon: Clock },
      { key: 'summary', label: 'Summary', icon: CheckCircle },
    ];

    return (
      <div className="flex justify-between mb-8 px-2">
        {steps.map((step, index) => {
          const isActive = currentStep === step.key;
          const isPast = steps.findIndex(s => s.key === currentStep) > steps.findIndex(s => s.key === step.key);
          const StepIcon = step.icon;

          return (
            <div key={step.key} className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${isActive ? 'bg-indigo-600 text-white' : isPast ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}
              >
                <StepIcon className="h-5 w-5" />
              </div>
              <span className={`text-xs mt-1 ${isActive ? 'text-indigo-600 font-medium' : isPast ? 'text-green-600' : 'text-gray-500'}`}>
                {step.label}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  // Render client selection step
  const renderClientStep = () => (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search clients by name or phone..."
          value={clientSearch}
          onChange={(e) => setClientSearch(e.target.value)}
          className="pl-10 bg-white shadow-sm"
        />
      </div>

      {/* Add New Client Button */}
      <div className="flex justify-end">
        <Button
          variant="outline"
          className="flex items-center text-indigo-600 border-indigo-200 hover:bg-indigo-50"
          onClick={() => setShowAddClientDialog(true)}
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Add New Client
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto p-1">
        {filteredClients.length > 0 ? (
          filteredClients.map((client) => (
            <div
              key={client.client_id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedClient === client.client_id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/50'}`}
              onClick={() => setSelectedClient(client.client_id)}
            >
              <div className="flex items-start">
                <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                  <User className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">{client.first_name} {client.last_name}</p>
                  <p className="text-sm text-gray-500">{client.phone || client.email || 'No contact info'}</p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-2 text-center py-8 text-gray-500">
            No clients found. Try a different search term or add a new client.
          </div>
        )}
      </div>
    </div>
  );

  // Render service selection step
  const renderServiceStep = () => (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search services..."
          value={serviceSearch}
          onChange={(e) => setServiceSearch(e.target.value)}
          className="pl-10 bg-white shadow-sm"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto p-1">
        {filteredServices.length > 0 ? (
          filteredServices.map((service) => (
            <div
              key={service.service_id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedServices.includes(service.service_id)
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/50'
              }`}
              onClick={() => handleServiceSelection(service.service_id)}
            >
              <div className="flex items-start">
                <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mr-3">
                  <Scissors className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <p className="font-medium">{service.name}</p>
                    <p className="text-sm text-gray-600">₹{service.price}</p>
                  </div>
                  <p className="text-sm text-gray-500">{service.duration} min</p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-2 text-center py-8 text-gray-500">
            No services found. Try a different search term.
          </div>
        )}
      </div>
    </div>
  );

  // Render staff selection step
  const renderStaffStep = () => (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search staff..."
          value={staffSearch}
          onChange={(e) => setStaffSearch(e.target.value)}
          className="pl-10 bg-white shadow-sm"
        />
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto p-1">
        {filteredStaff.length > 0 ? (
          filteredStaff.map((staffMember) => (
            <div
              key={staffMember.user_id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedStaff === staffMember.user_id ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:border-indigo-300 hover:bg-indigo-50/50'}`}
              onClick={() => setSelectedStaff(staffMember.user_id)}
            >
              <div className="flex items-start">
                <div className="h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 mr-3">
                  <CalendarCheck className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">{staffMember.first_name} {staffMember.last_name}</p>
                  <p className="text-sm text-gray-500">{staffMember.role || 'Staff'}</p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-2 text-center py-8 text-gray-500">
            No staff members found. Try a different search term.
          </div>
        )}
      </div>
    </div>
  );

  // Render date and time selection step
  const renderDateTimeStep = () => {
    // Generate next 14 days for date selection
    const generateDateOptions = () => {
      const dates = [];
      const today = new Date();
      
      for (let i = 0; i < 14; i++) {
        const date = new Date();
        date.setDate(today.getDate() + i);
        dates.push(date);
      }
      
      return dates;
    };
    
    // Generate time slots from 9 AM to 9 PM
    const generateTimeSlots = () => {
      const slots = [];
      for (let hour = 9; hour <= 21; hour++) {
        const hourStr = hour <= 12 ? `${hour}:00` : `${hour-12}:00`;
        const amPm = hour < 12 ? 'AM' : 'PM';
        slots.push(`${hourStr} ${amPm}`);
        
        if (hour !== 21) { // Don't add :30 for 9 PM
          const halfHourStr = hour <= 12 ? `${hour}:30` : `${hour-12}:30`;
          slots.push(`${halfHourStr} ${amPm}`);
        }
      }
      return slots;
    };
    
    const dateOptions = generateDateOptions();
    const timeSlots = generateTimeSlots();
    
    const formatDateOption = (date: Date) => {
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const day = days[date.getDay()];
      const month = months[date.getMonth()];
      const dateNum = date.getDate();
      
      const isToday = new Date().setHours(0,0,0,0) === date.setHours(0,0,0,0);
      
      return {
        display: `${day}, ${month} ${dateNum}${isToday ? ' (Today)' : ''}`,
        value: format(date, 'yyyy-MM-dd')
      };
    };

    // Check if a time slot is in the past for the current day
    const isTimeSlotPast = (timeSlot: string, dateValue: string) => {
      const now = new Date();
      const today = format(now, 'yyyy-MM-dd');
      
      // Only check for today
      if (dateValue !== today) return false;
      
      const [hourMin, period] = timeSlot.split(' ');
      const [hour, minute] = hourMin.split(':');
      let hour24 = parseInt(hour);
      if (period === 'PM' && hour24 !== 12) hour24 += 12;
      if (period === 'AM' && hour24 === 12) hour24 = 0;
      
      // Create a date object for the time slot
      const slotTime = new Date();
      slotTime.setHours(hour24, parseInt(minute), 0, 0);
      
      // Add a buffer (e.g., 30 minutes) to prevent booking too close to current time
      const bufferTime = new Date();
      bufferTime.setMinutes(bufferTime.getMinutes() + 30);
      
      return slotTime < bufferTime;
    };

    // Check if a time slot is already booked
    const isTimeSlotBooked = (timeSlot: string, dateValue: string) => {
      if (!selectedStaff || bookedAppointments.length === 0) return false;
      
      // 1. Convert the timeSlot to the format used in your database
      const [hourMin, period] = timeSlot.split(' ');
      const [hour, minute] = hourMin.split(':');
      let hour24 = parseInt(hour);
      if (period === 'PM' && hour24 !== 12) hour24 += 12;
      if (period === 'AM' && hour24 === 12) hour24 = 0;
      
      const timeValue = `${hour24.toString().padStart(2, '0')}:${minute}`;
      
      // 2. Create a datetime string for the selected slot
      const slotDateTime = new Date(`${dateValue}T${timeValue}`);
      
      // Calculate the end time of this slot (assuming 30 min duration for a slot)
      const slotEndTime = new Date(slotDateTime);
      slotEndTime.setMinutes(slotEndTime.getMinutes() + 30);
      
      // 3. Check if this slot overlaps with any booked appointment
      return bookedAppointments.some(appointment => {
        const appointmentStart = new Date(appointment.appointment_date);
        const appointmentEnd = new Date(appointmentStart);
        appointmentEnd.setMinutes(appointmentEnd.getMinutes() + appointment.duration);
        
        // Check for overlap
        return (
          (slotDateTime >= appointmentStart && slotDateTime < appointmentEnd) || // Slot start time is within appointment
          (slotEndTime > appointmentStart && slotEndTime <= appointmentEnd) || // Slot end time is within appointment
          (slotDateTime <= appointmentStart && slotEndTime >= appointmentEnd) // Slot completely contains appointment
        );
      });
    };

    return (
      <div className="space-y-8">
        <div>
          <h3 className="text-xl font-semibold mb-4">Select Date & Time</h3>
          
          {/* Date Selection */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <Calendar className="h-5 w-5 mr-2 text-gray-700" />
              <h4 className="font-medium">Select Date</h4>
            </div>
            
            <div className="grid grid-cols-4 gap-2">
              {dateOptions.map((dateOption, index) => {
                const { display, value } = formatDateOption(dateOption);
                const isSelected = date === value;
                const isToday = display.includes('(Today)');
                
                return (
                  <div 
                    key={index}
                    onClick={() => setDate(value)}
                    className={`
                      p-2 rounded-md text-center cursor-pointer text-sm transition-colors
                      ${isSelected 
                        ? isToday 
                          ? 'bg-gray-900 text-white' 
                          : 'bg-blue-600 text-white' 
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}
                    `}
                  >
                    {display.split(',')[0]},<br/>{display.split(',')[1]}
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* Time Selection */}
          <div>
            <div className="flex items-center mb-3">
              <Clock className="h-5 w-5 mr-2 text-gray-700" />
              <h4 className="font-medium">Select Time</h4>
            </div>
            
            <div className="grid grid-cols-4 gap-2">
              {timeSlots.map((timeSlot, index) => {
                // Convert display time (e.g. "9:00 AM") to 24h format for comparison
                const [hourMin, period] = timeSlot.split(' ');
                const [hour, minute] = hourMin.split(':');
                let hour24 = parseInt(hour);
                if (period === 'PM' && hour24 !== 12) hour24 += 12;
                if (period === 'AM' && hour24 === 12) hour24 = 0;
                
                const timeValue = `${hour24.toString().padStart(2, '0')}:${minute}`;
                const isSelected = time === timeValue;
                const isPast = isTimeSlotPast(timeSlot, date);
                const isBooked = isTimeSlotBooked(timeSlot, date);
                const isDisabled = isPast || isBooked;
                
                return (
                  <div 
                    key={index}
                    onClick={() => !isDisabled && setTime(timeValue)}
                    className={`
                      p-2 rounded-md text-center transition-colors
                      ${isDisabled 
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                        : isSelected 
                          ? 'bg-blue-600 text-white cursor-pointer' 
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 cursor-pointer'}
                      ${isBooked ? 'line-through' : ''}
                    `}
                    title={isBooked ? 'Already booked' : isPast ? 'Time slot has passed' : ''}
                  >
                    {timeSlot}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render summary step
  const renderSummaryStep = () => (
    <div className="space-y-6">
      <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-100">
        <h3 className="font-medium text-indigo-800 mb-2">Appointment Summary</h3>

        <div className="space-y-3">
          <div className="flex">
            <User className="h-5 w-5 text-indigo-500 mr-3 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-gray-700">Client</p>
              <p className="text-sm text-gray-600">{selectedClientName}</p>
            </div>
          </div>

          <div className="flex">
            <Scissors className="h-5 w-5 text-indigo-500 mr-3 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-gray-700">Services</p>
              {selectedServicesDetails.map((service, index) => (
                <p key={index} className="text-sm text-gray-600">
                  {service.name} - ₹{service.price}
                </p>
              ))}
            </div>
          </div>

          <div className="flex">
            <CalendarCheck className="h-5 w-5 text-indigo-500 mr-3 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-gray-700">Staff</p>
              <p className="text-sm text-gray-600">{selectedStaffName}</p>
            </div>
          </div>

          <div className="flex">
            <Calendar className="h-5 w-5 text-indigo-500 mr-3 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-gray-700">Date & Time</p>
              <p className="text-sm text-gray-600">
                {date ? format(new Date(date), 'MMMM d, yyyy') : ''} at {time || ''}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-green-50 p-4 rounded-lg border border-green-100">
        <div className="flex items-center">
          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
          <p className="text-sm text-green-800">Ready to book this appointment?</p>
        </div>
      </div>
    </div>
  );

  // Render the current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'client':
        return renderClientStep();
      case 'service':
        return renderServiceStep();
      case 'staff':
        return renderStaffStep();
      case 'datetime':
        return renderDateTimeStep();
      case 'summary':
        return renderSummaryStep();
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-lg sm:max-w-[650px] w-full mx-4 p-6 max-h-[90vh] overflow-y-auto">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-center">Book New Appointment</h2>
        </div>

        {renderStepIndicators()}

        <div className="mb-6">
          {renderStepContent()}
        </div>

        <div className="flex justify-between mt-6">
          {currentStep !== 'client' ? (
            <Button
              variant="outline"
              onClick={handleBack}
              className="flex items-center"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          ) : (
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          )}

          {currentStep !== 'summary' ? (
            <Button
              onClick={handleNext}
              className="bg-indigo-600 hover:bg-indigo-700 text-white flex items-center"
            >
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              className="bg-green-600 hover:bg-green-700 text-white flex items-center"
            >
              Book Appointment
              <CheckCircle className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Add Client Dialog */}
      <AddClientDialog
        isOpen={showAddClientDialog}
        onClose={() => setShowAddClientDialog(false)}
        onSubmit={handleAddClient}
      />
    </div>
  );
}
