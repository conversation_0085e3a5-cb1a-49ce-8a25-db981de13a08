<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp Bot Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; box-sizing: border-box; }
        button { background: #25D366; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WhatsApp Bot Local Test</h1>
        
        <div class="form-group">
            <label>Salon ID:</label>
            <input type="text" id="salonId" value="d0a56955-eba7-4679-b0de-c9946505c1af">
        </div>
        
        <div class="form-group">
            <label>Phone Number (10 digits):</label>
            <input type="text" id="phoneNumber" value="9025412115" placeholder="e.g., 9876543210">
        </div>
        
        <div class="form-group">
            <label>Message:</label>
            <textarea id="message" rows="3">🧪 Test message from local WhatsApp bot! This is to verify the connection is working properly.</textarea>
        </div>
        
        <button onclick="checkStatus()">Check Status</button>
        <button onclick="sendMessage()">Send Message</button>
        <button onclick="getQR()">Get QR Code</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        function showResult(message, isError = false) {
            const result = document.getElementById('result');
            result.style.display = 'block';
            result.className = 'result ' + (isError ? 'error' : 'success');
            result.innerHTML = '<pre>' + JSON.stringify(message, null, 2) + '</pre>';
        }
        
        async function checkStatus() {
            const salonId = document.getElementById('salonId').value;
            try {
                const response = await fetch(`${API_BASE}/session/${salonId}/status`);
                const result = await response.json();
                showResult(result, !response.ok);
            } catch (error) {
                showResult({error: error.message}, true);
            }
        }
        
        async function sendMessage() {
            const salonId = document.getElementById('salonId').value;
            const phoneNumber = document.getElementById('phoneNumber').value;
            const message = document.getElementById('message').value;
            
            try {
                const response = await fetch(`${API_BASE}/session/${salonId}/send`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phoneNumber: phoneNumber,
                        message: message
                    })
                });
                const result = await response.json();
                showResult(result, !response.ok);
            } catch (error) {
                showResult({error: error.message}, true);
            }
        }
        
        async function getQR() {
            const salonId = document.getElementById('salonId').value;
            try {
                const response = await fetch(`${API_BASE}/session/${salonId}/qr`);
                const result = await response.json();
                showResult(result, !response.ok);
            } catch (error) {
                showResult({error: error.message}, true);
            }
        }
    </script>
</body>
</html>
