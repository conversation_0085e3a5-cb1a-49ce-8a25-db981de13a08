import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './SimpleAuthContext';
import { supabase } from './SimpleAuthContext';

interface UserProfileData {
  first_name: string;
  last_name: string;
  email: string;
}

interface UserProfileContextType {
  userProfile: UserProfileData | null;
  loading: boolean;
  updateUserProfile: (data: Partial<UserProfileData>) => void;
  refreshUserProfile: () => Promise<void>;
  getFullName: () => string;
  getInitials: () => string;
}

const UserProfileContext = createContext<UserProfileContextType | undefined>(undefined);

export const useUserProfile = () => {
  const context = useContext(UserProfileContext);
  if (context === undefined) {
    throw new Error('useUserProfile must be used within a UserProfileProvider');
  }
  return context;
};

interface UserProfileProviderProps {
  children: ReactNode;
}

export const UserProfileProvider: React.FC<UserProfileProviderProps> = ({ children }) => {
  const [userProfile, setUserProfile] = useState<UserProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const fetchUserProfile = async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('users')
        .select('first_name, last_name')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        setUserProfile(null);
      } else if (data) {
        setUserProfile({
          first_name: data.first_name || '',
          last_name: data.last_name || '',
          email: user.email || '',
        });
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const updateUserProfile = (data: Partial<UserProfileData>) => {
    setUserProfile(prev => prev ? { ...prev, ...data } : null);
  };

  const refreshUserProfile = async () => {
    await fetchUserProfile();
  };

  const getFullName = () => {
    if (!userProfile) return user?.email || 'User';
    const fullName = `${userProfile.first_name} ${userProfile.last_name}`.trim();
    return fullName || user?.email || 'User';
  };

  const getInitials = () => {
    if (!userProfile) {
      return user?.email ? user.email[0].toUpperCase() : 'U';
    }

    const { first_name, last_name } = userProfile;
    
    if (first_name && last_name) {
      return `${first_name[0]}${last_name[0]}`.toUpperCase();
    } else if (first_name) {
      return first_name[0].toUpperCase();
    } else if (user?.email) {
      return user.email[0].toUpperCase();
    }
    
    return 'U';
  };

  useEffect(() => {
    fetchUserProfile();
  }, [user]);

  const value = {
    userProfile,
    loading,
    updateUserProfile,
    refreshUserProfile,
    getFullName,
    getInitials,
  };

  return <UserProfileContext.Provider value={value}>{children}</UserProfileContext.Provider>;
};
