import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, Clock, User, Loader2, Plus, Search, Filter, IndianRupee, Check, X } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { AppointmentDialog } from '../components/AppointmentDialog';
import { BookAppointmentDialog } from '../components/BookAppointmentDialogNew';
import { AppointmentStatus } from '../types/appointment';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Input } from '../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Tabs, Tab } from '../components/ui/tabs';
import { useToast } from '../components/ui/use-toast';
import { Pagination } from '../components/ui/pagination';
import { CardModern as Card, CardContent, CardHeader, CardTitle } from '../components/ui/card-modern';
import { MobileAppointmentsList } from '../components/mobile/MobileAppointmentsList';
import {
  Appointment,
  getAppointments,
  updateAppointmentStatus,
  deleteAppointment,
  addAppointment,
  getAppointment
} from '../services/appointmentService';
import { getCurrentUserSalonId } from '../services/salonService';
import { Client, getClients } from '../services/salonService';
import { Service, getServices } from '../services/salonService';
import { StaffMember, getStaff } from '../services/salonService';

export function Appointments() {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  const [showBookDialog, setShowBookDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [salonId, setSalonId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [clients, setClients] = useState<Client[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();

  // Fetch salon ID and appointments on component mount
  useEffect(() => {
    const fetchSalonId = async () => {
      try {
        const id = await getCurrentUserSalonId();
        setSalonId(id);
        if (id) {
          fetchAppointments(id);
          fetchClients(id);
          fetchServices(id);
          fetchStaff(id);
        } else {
          setLoading(false);
          toast({
            title: 'Error',
            description: 'Could not determine your salon. Please try again later.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching salon ID:', error);
        setLoading(false);
      }
    };

    fetchSalonId();
  }, []);

  // Check for reschedule parameter in URL
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const rescheduleId = params.get('reschedule');

    if (rescheduleId && appointments.length > 0) {
      const appointmentToReschedule = appointments.find(a => a.appointment_id === rescheduleId);
      if (appointmentToReschedule) {
        setSelectedAppointment(appointmentToReschedule);
        setShowAppointmentDialog(true);
        // Clear the parameter from URL
        navigate('/appointments', { replace: true });
        toast({
          title: 'Reschedule Appointment',
          description: 'Please update the appointment details and save changes.',
        });
      }
    }
  }, [location.search, appointments, navigate, toast]);

  // Filter appointments when search query or filters change
  useEffect(() => {
    if (!appointments.length) {
      setFilteredAppointments([]);
      return;
    }

    let filtered = [...appointments];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter) {
      filtered = filtered.filter(appointment => {
        // Extract just the date part from the appointment_date timestamp
        const appointmentDate = appointment.appointment_date.split('T')[0];
        return appointmentDate === dateFilter;
      });
    }

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        appointment =>
          appointment.client_name?.toLowerCase().includes(query) ||
          appointment.service_name?.toLowerCase().includes(query) ||
          appointment.staff_name?.toLowerCase().includes(query)
      );
    }

    // Get current date and time for comparison
    const now = new Date().getTime();

    // Sort appointments by proximity to current time
    filtered.sort((a, b) => {
      const timeA = new Date(a.appointment_date).getTime();
      const timeB = new Date(b.appointment_date).getTime();
      
      // If filtering by date, sort by time within the day (ascending)
      if (dateFilter) {
        return timeA - timeB;
      }
      
      // For upcoming appointments (in the future), sort by nearest first
      const aInFuture = timeA >= now;
      const bInFuture = timeB >= now;
      
      if (aInFuture && bInFuture) {
        // Both in future, nearest first
        return timeA - timeB;
      } else if (aInFuture) {
        // Only A is in future, it comes first
        return -1;
      } else if (bInFuture) {
        // Only B is in future, it comes first
        return 1;
      } else {
        // Both in past, most recent first
        return timeB - timeA;
      }
    });

    setFilteredAppointments(filtered);
    // Reset to first page when filters change
    setCurrentPage(1);
  }, [appointments, searchQuery, statusFilter, dateFilter]);

  const fetchAppointments = async (id: string) => {
    setLoading(true);
    try {
      const data = await getAppointments(id);
      setAppointments(data);
      setFilteredAppointments(data);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load appointments. Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchClients = async (id: string) => {
    try {
      const data = await getClients(id);
      setClients(data);
    } catch (error) {
      console.error('Error fetching clients:', error);
    }
  };

  const fetchServices = async (id: string) => {
    try {
      const data = await getServices(id);
      setServices(data);
    } catch (error) {
      console.error('Error fetching services:', error);
    }
  };

  const fetchStaff = async (id: string) => {
    try {
      const data = await getStaff(id);
      setStaff(data);
    } catch (error) {
      console.error('Error fetching staff:', error);
    }
  };

  const handleStatusChange = async (appointmentId: string, status: AppointmentStatus) => {
    try {
      const updatedAppointment = await updateAppointmentStatus(appointmentId, status);
      if (updatedAppointment) {
        // Update the appointments list
        const updatedAppointments = appointments.map(appointment =>
          appointment.appointment_id === appointmentId ? { ...appointment, status } : appointment
        );
        setAppointments(updatedAppointments);

        // Also update the selectedAppointment if it's the one being modified
        if (selectedAppointment && selectedAppointment.appointment_id === appointmentId) {
          setSelectedAppointment({ ...selectedAppointment, status });
        }

        toast({
          title: 'Success',
          description: `Appointment ${status === 'checked-in' ? 'checked in' : status} successfully`,
        });

        // Close the dialog if the appointment was cancelled
        if (status === 'cancelled') {
          setShowAppointmentDialog(false);
        }
      }
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update appointment status. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteAppointment = async (appointmentId: string) => {
    if (confirm('Are you sure you want to cancel this appointment?')) {
      try {
        const success = await deleteAppointment(appointmentId);
        if (success) {
          setAppointments(appointments.filter(appointment => appointment.appointment_id !== appointmentId));
          setShowAppointmentDialog(false);
          toast({
            title: 'Success',
            description: 'Appointment cancelled successfully',
          });
        }
      } catch (error) {
        console.error('Error cancelling appointment:', error);
        toast({
          title: 'Error',
          description: 'Failed to cancel appointment. Please try again.',
          variant: 'destructive',
        });
      }
    }
  };

  const handleBookAppointment = async (appointmentData: {
    clientId: string;
    serviceIds: string[]; // Changed from serviceId to serviceIds array
    staffId: string;
    appointmentDate: string;
  }) => {
    if (!salonId) {
      toast({
        title: 'Error',
        description: 'Could not determine your salon. Please try again later.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const newAppointment = await addAppointment({
        salon_id: salonId,
        client_id: appointmentData.clientId,
        service_ids: appointmentData.serviceIds, // Already an array of service IDs
        staff_id: appointmentData.staffId,
        appointment_date: appointmentData.appointmentDate,
        status: 'scheduled',
      });

      if (newAppointment) {
        // Fetch the client, services, and staff details to display in the list
        const client = clients.find(c => c.client_id === appointmentData.clientId);
        const selectedServices = services.filter(s => appointmentData.serviceIds.includes(s.service_id));
        const staffMember = staff.find(s => s.user_id === appointmentData.staffId);

        const appointmentWithDetails = {
          ...newAppointment,
          client_name: client ? `${client.first_name} ${client.last_name}` : '',
          service_name: selectedServices.map(s => s.name).join(', '),
          staff_name: staffMember ? `${staffMember.first_name} ${staffMember.last_name}` : '',
        };

        // Add new appointment at the beginning of the array to show it at the top
        setAppointments([appointmentWithDetails, ...appointments]);
        toast({
          title: 'Success',
          description: 'Appointment booked successfully',
        });
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
      toast({
        title: 'Error',
        description: 'Failed to book appointment. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (status: AppointmentStatus) => {
    const variants = {
      pending: 'outline',
      scheduled: 'default',
      'checked-in': 'warning',
      completed: 'success',
      cancelled: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  const formatTime = (dateString: string) => {
    try {
      return format(new Date(dateString), 'h:mm a');
    } catch (error) {
      return '';
    }
  };



  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Listen for the custom event to open the appointment dialog
    const handleOpenAppointmentDialog = () => {
      setShowBookDialog(true);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('open-appointment-dialog', handleOpenAppointmentDialog);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('open-appointment-dialog', handleOpenAppointmentDialog);
    };
  }, []);

  return (
    <div className="space-y-6">
      {isMobile ? (
        // Mobile view
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-indigo-500" />
              <Input
                placeholder="Search appointments..."
                className="pl-10 rounded-full border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 shadow-sm bg-white"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center px-2 py-2 bg-white/80 rounded-lg shadow-sm border border-indigo-50">
              <div className="h-6 w-6 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-600 flex-shrink-0 mr-2">
                <Filter className="h-3 w-3" />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="border-0 bg-transparent h-7 min-h-0 p-0 focus:ring-0">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="checked-in">Checked In</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center px-2 py-2 bg-white/80 rounded-lg shadow-sm border border-indigo-50">
              <div className="h-6 w-6 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-600 flex-shrink-0 mr-2">
                <CalendarIcon className="h-3 w-3" />
              </div>
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="border-0 bg-transparent h-7 min-h-0 p-0 focus:ring-0"
              />
            </div>
          </div>
        </div>
      ) : (
        // Desktop view
        <Card gradient hover>
          <CardHeader className="flex flex-col sm:flex-row justify-between items-center">
            <CardTitle className="flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2 text-indigo-600" />
              Appointments
            </CardTitle>
            <Button
              onClick={() => setShowBookDialog(true)}
              className="mt-4 sm:mt-0 bg-indigo-600 hover:bg-indigo-700 rounded-full shadow-sm"
            >
              <Plus className="h-5 w-5 mr-2" />
              Book Appointment
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Search appointments..."
                    className="pl-10 rounded-lg border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-600 flex-shrink-0">
                  <Filter className="h-4 w-4" />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="bg-white rounded-lg border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="checked-in">Checked In</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-indigo-50 flex items-center justify-center text-indigo-600 flex-shrink-0">
                  <CalendarIcon className="h-4 w-4" />
                </div>
                <Input
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="bg-white rounded-lg border-gray-200 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Tabs */}
      <Tabs className="mb-4">
        <Tab
          active={statusFilter === 'all'}
          onClick={() => setStatusFilter('all')}
        >
          All
        </Tab>
        <Tab
          active={statusFilter === 'pending'}
          onClick={() => setStatusFilter('pending')}
        >
          Pending
        </Tab>
        <Tab
          active={statusFilter === 'scheduled'}
          onClick={() => setStatusFilter('scheduled')}
        >
          Scheduled
        </Tab>
        <Tab
          active={statusFilter === 'checked-in'}
          onClick={() => setStatusFilter('checked-in')}
        >
          Checked In
        </Tab>
        <Tab
          active={statusFilter === 'completed'}
          onClick={() => setStatusFilter('completed')}
        >
          Completed
        </Tab>
        <Tab
          active={statusFilter === 'cancelled'}
          onClick={() => setStatusFilter('cancelled')}
        >
          Cancelled
        </Tab>
      </Tabs>

      {isMobile ? (
        // Mobile view of appointments list
        <div className="rounded-xl overflow-hidden shadow-sm border border-indigo-50">
          <MobileAppointmentsList
            appointments={filteredAppointments.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)}
            onEdit={async (appointment) => {
              // Fetch the latest appointment data before showing the dialog
              try {
                console.log('Mobile view: Fetching appointment details for:', appointment.appointment_id);
                const latestAppointment = await getAppointment(appointment.appointment_id);
                console.log('Mobile view: Latest appointment data received:', latestAppointment);

                if (latestAppointment) {
                  // Ensure payment method is preserved
                  console.log('Mobile view: Payment method in fetched appointment:', latestAppointment.paymentMethod);

                  // Map the appointment data to the format expected by AppointmentDialog
                  const mappedAppointment = {
                    id: latestAppointment.appointment_id,
                    appointment_id: latestAppointment.appointment_id,
                    client: latestAppointment.client_name,
                    services: latestAppointment.services || [],
                    appointmentDate: latestAppointment.appointment_date,
                    stylist: latestAppointment.staff_name,
                    price: latestAppointment.price || 0,
                    status: latestAppointment.status,
                    notes: latestAppointment.notes,
                    paymentMethod: latestAppointment.paymentMethod // Explicitly map the payment method
                  };

                  console.log('Mobile view: Mapped appointment with payment method:', mappedAppointment);
                  setSelectedAppointment(mappedAppointment);
                } else {
                  console.log('Mobile view: No appointment data received, using existing data');
                  setSelectedAppointment(appointment);
                }
                setShowAppointmentDialog(true);
              } catch (error) {
                console.error('Error fetching appointment details:', error);
                setSelectedAppointment(appointment);
                setShowAppointmentDialog(true);
              }
            }}
            onCancel={(id) => handleStatusChange(id, 'cancelled')}
            onCheckIn={(id) => handleStatusChange(id, 'checked-in')}
            onCheckOut={(id) => handleStatusChange(id, 'completed')}
            loading={loading}
            searchQuery={searchQuery}
          />

          {/* Pagination for mobile */}
          {!loading && filteredAppointments.length > 0 && (
            <div className="px-4 py-3 border-t border-gray-100 bg-gradient-to-r from-indigo-50 to-purple-50">
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(filteredAppointments.length / itemsPerPage)}
                onPageChange={setCurrentPage}
                totalItems={filteredAppointments.length}
                itemsPerPage={itemsPerPage}
              />
            </div>
          )}
        </div>
      ) : (
        // Desktop view
        loading ? (
          <Card gradient className="py-10">
            <div className="flex justify-center items-center">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            </div>
          </Card>
        ) : filteredAppointments.length === 0 ? (
          <Card gradient hover className="p-10 text-center">
            <div className="flex flex-col items-center">
              <CalendarIcon className="h-12 w-12 text-gray-300 mb-4" />
              <p className="text-gray-500 mb-4">
                {searchQuery || statusFilter !== 'all' || dateFilter
                  ? 'No appointments match your search criteria'
                  : 'No appointments found. Book your first appointment!'}
              </p>
              {!(searchQuery || statusFilter !== 'all' || dateFilter) && (
                <Button
                  onClick={() => setShowBookDialog(true)}
                  variant="outline"
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Book Appointment
                </Button>
              )}
            </div>
          </Card>
        ) : (
          <Card gradient hover>
            <div className="divide-y divide-gray-100">
              {/* Get current appointments for pagination */}
              {filteredAppointments
                .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                .map((appointment) => {
                  const statusVariants = {
                    'pending': 'bg-purple-50 text-purple-700 border-purple-100',
                    'scheduled': 'bg-blue-50 text-blue-700 border-blue-100',
                    'checked-in': 'bg-amber-50 text-amber-700 border-amber-100',
                    'completed': 'bg-green-50 text-green-700 border-green-100',
                    'cancelled': 'bg-red-50 text-red-700 border-red-100',
                  };

                  return (
                    <div
                      key={appointment.appointment_id}
                      className="p-4 cursor-pointer hover:bg-gray-50/50 transition-colors"
                      onClick={async () => {
                        // Fetch the latest appointment data before showing the dialog
                        try {
                          console.log('Fetching appointment details for:', appointment.appointment_id);
                          const latestAppointment = await getAppointment(appointment.appointment_id);
                          console.log('Latest appointment data received:', latestAppointment);

                          if (latestAppointment) {
                            // Ensure payment method is preserved
                            console.log('Payment method in fetched appointment:', latestAppointment.paymentMethod);

                            // Map the appointment data to the format expected by AppointmentDialog
                            const mappedAppointment = {
                              id: latestAppointment.appointment_id,
                              appointment_id: latestAppointment.appointment_id,
                              client: latestAppointment.client_name,
                              services: latestAppointment.services || [],
                              appointmentDate: latestAppointment.appointment_date,
                              stylist: latestAppointment.staff_name,
                              price: latestAppointment.price || 0,
                              status: latestAppointment.status,
                              notes: latestAppointment.notes,
                              paymentMethod: latestAppointment.paymentMethod // Explicitly map the payment method
                            };

                            console.log('Mapped appointment with payment method:', mappedAppointment);
                            setSelectedAppointment(mappedAppointment);
                          } else {
                            console.log('No appointment data received, using existing data');
                            setSelectedAppointment(appointment);
                          }
                          setShowAppointmentDialog(true);
                        } catch (error) {
                          console.error('Error fetching appointment details:', error);
                          setSelectedAppointment(appointment);
                          setShowAppointmentDialog(true);
                        }
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 flex-shrink-0">
                            <User className="h-5 w-5" />
                          </div>
                          <div>
                            <div className="flex flex-wrap items-center gap-2">
                              <span className="font-medium text-gray-900">{appointment.client_name}</span>
                              <span className={`text-xs px-2 py-0.5 rounded-full border ${statusVariants[appointment.status]}`}>
                                {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-0.5">{appointment.service_name}</p>
                            <div className="flex flex-wrap items-center text-xs text-gray-500 mt-1 gap-x-3 gap-y-1">
                              <div className="flex items-center">
                                <CalendarIcon className="mr-1 h-3.5 w-3.5" />
                                {formatDate(appointment.appointment_date)}
                              </div>
                              <div className="flex items-center">
                                <Clock className="mr-1 h-3.5 w-3.5" />
                                {formatTime(appointment.appointment_date)}
                              </div>
                              <div className="flex items-center">
                                <User className="mr-1 h-3.5 w-3.5" />
                                {appointment.staff_name}
                              </div>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="rounded-full h-8 w-8 p-0 flex items-center justify-center text-indigo-600"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                          </svg>
                        </Button>
                      </div>
                    </div>
                  );
                })}
            </div>

            {/* Pagination */}
            {filteredAppointments.length > 0 && (
              <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(filteredAppointments.length / itemsPerPage)}
                  onPageChange={setCurrentPage}
                  totalItems={filteredAppointments.length}
                  itemsPerPage={itemsPerPage}
                />
              </div>
            )}
          </Card>
        )
      )}

      {selectedAppointment && (
        <AppointmentDialog
          isOpen={showAppointmentDialog}
          onClose={() => setShowAppointmentDialog(false)}
          appointment={{
            id: selectedAppointment.appointment_id || selectedAppointment.id,
            client: selectedAppointment.client_name || selectedAppointment.client || '',
            services: selectedAppointment.services || [],
            appointmentDate: selectedAppointment.appointment_date || selectedAppointment.appointmentDate,
            stylist: selectedAppointment.staff_name || selectedAppointment.stylist || '',
            status: selectedAppointment.status,
            price: selectedAppointment.price || 0,
            paymentMethod: selectedAppointment.paymentMethod, // Include the payment method
          }}
          onStatusChange={(status) => handleStatusChange(selectedAppointment.appointment_id, status)}
          onReschedule={() => {
            setShowAppointmentDialog(false);
            fetchAppointments();
          }}
          onCancel={() => {
            handleStatusChange(selectedAppointment.appointment_id, 'cancelled');
            setShowAppointmentDialog(false);
          }}
          staffMembers={staff.map(s => ({
            id: s.user_id,
            name: `${s.first_name} ${s.last_name}`
          }))}
          services={services.map(s => ({
            id: s.service_id,
            name: s.name,
            price: s.price
          }))}
        />
      )}

      <BookAppointmentDialog
        isOpen={showBookDialog}
        onClose={() => setShowBookDialog(false)}
        onSubmit={handleBookAppointment}
        clients={clients}
        services={services}
        staff={staff}
      />

      {/* We don't need a floating action button here as it's handled by MobileNavBar */}
    </div>
  );
}
