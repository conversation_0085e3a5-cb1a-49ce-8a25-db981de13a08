// Test script to manually trigger the WhatsApp queue processor
// Now using Railway deployment: https://wb-userbot-production.up.railway.app
const fetch = require("node-fetch");

async function triggerQueueProcessor() {
  try {
    console.log("🚀 Triggering WhatsApp queue processor...");

    const response = await fetch(
      "https://mixjfinrxzpplzqidlas.supabase.co/functions/v1/whatsapp-queue-processor",
      {
        method: "POST",
        headers: {
          "Authorization":
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1peGpmaW5yeHpwcGx6cWlkbGFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1ODY0MzgsImV4cCI6MjA1OTE2MjQzOH0.g4lTxBEZbG_GJKy_WrBJwW0H2tr9XaZHLzXpjdtbCzA",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({}),
      },
    );

    const result = await response.json();
    console.log("📊 Queue processor result:", result);

    if (result.success) {
      console.log(`✅ Processed: ${result.processed} messages`);
      console.log(`📤 Sent: ${result.sent} messages`);
      console.log(`❌ Failed: ${result.failed} messages`);
      console.log(`👥 Unique recipients: ${result.unique_recipients}`);
    } else {
      console.log("❌ Queue processor failed:", result.message);
    }
  } catch (error) {
    console.error("❌ Error triggering queue processor:", error.message);
  }
}

// Run the test
triggerQueueProcessor();
